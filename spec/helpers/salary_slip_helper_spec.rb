require 'rails_helper'

RSpec.describe SalarySlipHelper, type: :helper do
  let(:config) { SalarySlipConfig.new(currency_symbol: 'JOD', currency_position: 'after') }
  let(:employee) { build(:employee, name: '<PERSON>', department: 'Engineering') }
  let(:salary_package) { build(:salary_package, base_salary: 3000) }
  let(:salary_calculation) do
    build(:salary_calculation,
          employee: employee,
          salary_package: salary_package,
          gross_salary: 3000,
          net_salary: 2700,
          period: '2025-01',
          deductions: { 'income_tax' => 200, 'social_security' => 100 })
  end

  before do
    @config = config
    @employee = employee
    @salary_calculation = salary_calculation
  end

  describe '#format_currency' do
    it 'formats currency with symbol after amount' do
      expect(helper.format_currency(1000)).to eq('1000.00 JOD')
    end

    it 'handles zero amounts' do
      expect(helper.format_currency(0)).to eq('0.00 JOD')
    end

    it 'handles nil amounts' do
      expect(helper.format_currency(nil)).to eq('0.00 JOD')
    end

    context 'with currency before amount' do
      before { @config.currency_position = 'before' }

      it 'formats currency with symbol before amount' do
        expect(helper.format_currency(1000)).to eq('JOD1000.00')
      end
    end
  end

  describe '#format_salary_month' do
    it 'formats standard YYYY-MM period' do
      expect(helper.format_salary_month('2025-01')).to eq('January, 2025')
    end

    it 'formats custom period with same month' do
      expect(helper.format_salary_month('2025-01-01_2025-01-31')).to eq('January, 2025')
    end

    it 'formats custom period with different months' do
      expect(helper.format_salary_month('2025-01-15_2025-02-15')).to eq('Jan 2025 - Feb 2025')
    end

    it 'handles invalid periods' do
      expect(helper.format_salary_month('invalid')).to eq('invalid')
    end

    it 'handles blank periods' do
      expect(helper.format_salary_month('')).to eq('Unknown Period')
    end
  end

  describe '#calculate_total_taxes' do
    it 'calculates total from tax-related deductions' do
      expect(helper.calculate_total_taxes).to eq(200)
    end

    context 'with multiple tax types' do
      before do
        @salary_calculation.deductions = {
          'income_tax' => 150,
          'tax' => 50,
          'social_security' => 100
        }
      end

      it 'sums all tax-related deductions' do
        expect(helper.calculate_total_taxes).to eq(200)
      end
    end

    context 'with no tax deductions' do
      before { @salary_calculation.deductions = { 'social_security' => 100 } }

      it 'returns zero' do
        expect(helper.calculate_total_taxes).to eq(0)
      end
    end
  end

  describe '#deduction_description' do
    it 'returns description for income tax' do
      expect(helper.deduction_description('income_tax')).to include('Income tax deduction')
    end

    it 'returns description for social security' do
      expect(helper.deduction_description('social_security')).to include('Social security contribution')
    end

    it 'returns generic description for unknown types' do
      expect(helper.deduction_description('unknown')).to include('Unknown deduction')
    end
  end

  describe '#employee_department_display' do
    it 'returns department name when available' do
      allow(@employee).to receive(:department_name).and_return('Engineering')
      expect(helper.employee_department_display).to eq('Engineering')
    end

    it 'falls back to department when department_name is blank' do
      allow(@employee).to receive(:department_name).and_return(nil)
      allow(@employee).to receive(:department).and_return('ENG')
      expect(helper.employee_department_display).to eq('ENG')
    end

    it 'returns Employee when both are blank' do
      allow(@employee).to receive(:department_name).and_return(nil)
      allow(@employee).to receive(:department).and_return(nil)
      expect(helper.employee_department_display).to eq('Employee')
    end
  end

  describe '#display_allowance?' do
    it 'returns true for positive amounts' do
      expect(helper.display_allowance?(100)).to be_truthy
    end

    it 'returns false for zero amounts' do
      expect(helper.display_allowance?(0)).to be_falsey
    end

    it 'returns false for nil amounts' do
      expect(helper.display_allowance?(nil)).to be_falsey
    end
  end
end
