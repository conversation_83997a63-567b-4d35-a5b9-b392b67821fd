# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Attendance::DeviceCommand, type: :model do
  let(:device) { create(:attendance_device) }
  let(:adapter) { double('adapter') }
  let(:command_class) { double('command_class') }
  let(:command_instance) { double('command_instance') }

  before do
    allow(device).to receive(:create_adapter).and_return(adapter)
  end

  describe '.for_device' do
    context 'when device supports commands' do
      before do
        allow(adapter).to receive(:supports_commands?).and_return(true)
        allow(adapter).to receive(:available_commands).and_return([ 'test_command' ])
        allow(adapter).to receive(:get_command_class).with('test_command').and_return(command_class)
        allow(command_class).to receive(:command_name).and_return('Test Command')
        allow(command_class).to receive(:description).and_return('Test description')
        allow(command_class).to receive(:attribute_names).and_return([ 'param1' ])
        allow(command_class).to receive(:attribute_types).and_return({ 'param1' => double(type: :string) })
        allow(command_class).to receive(:required_parameters).and_return([ 'param1' ])
      end

      it 'returns array of DeviceCommand instances' do
        commands = described_class.for_device(device)

        expect(commands).to be_an(Array)
        expect(commands.first).to be_a(described_class)
        expect(commands.first.name).to eq('test_command')
        expect(commands.first.display_name).to eq('Test Command')
      end
    end

    context 'when device does not support commands' do
      before do
        allow(adapter).to receive(:supports_commands?).and_return(false)
      end

      it 'returns empty array' do
        commands = described_class.for_device(device)
        expect(commands).to eq([])
      end
    end
  end

  describe '.validate_for_device' do
    context 'when device does not support commands' do
      before do
        allow(adapter).to receive(:supports_commands?).and_return(false)
      end

      it 'returns validation error' do
        result = described_class.validate_for_device(device, 'test_command', {})

        expect(result[:valid]).to be false
        expect(result[:error]).to eq("Device does not support commands")
      end
    end

    context 'when command is not supported' do
      before do
        allow(adapter).to receive(:supports_commands?).and_return(true)
        allow(adapter).to receive(:get_command_class).with('unknown_command').and_return(nil)
      end

      it 'returns validation error' do
        result = described_class.validate_for_device(device, 'unknown_command', {})

        expect(result[:valid]).to be false
        expect(result[:error]).to include("Command 'unknown_command' not supported")
      end
    end

    context 'when command is valid' do
      before do
        allow(adapter).to receive(:supports_commands?).and_return(true)
        allow(adapter).to receive(:get_command_class).with('test_command').and_return(command_class)
        allow(command_class).to receive(:new).with({ param: 'value' }).and_return(command_instance)
        allow(command_instance).to receive(:validation_result).and_return({ valid: true })
      end

      it 'returns successful validation' do
        result = described_class.validate_for_device(device, 'test_command', { param: 'value' })

        expect(result[:valid]).to be true
      end
    end
  end

  describe '.execute_for_device' do
    let(:employee) { create(:employee) }
    let(:execution) { create(:attendance_command_execution, device: device) }
    let(:command_result) { Attendance::CommandResult.success('Command executed') }

    before do
      allow(Attendance::CommandExecution).to receive(:create!).and_return(execution)
      allow(execution).to receive(:update!)
    end

    context 'when device does not support commands' do
      before do
        allow(adapter).to receive(:supports_commands?).and_return(false)
      end

      it 'returns failure result and marks execution as failed' do
        result = described_class.execute_for_device(device, 'test_command', {}, employee)

        expect(result.success).to be false
        expect(result.error).to eq("Device does not support commands")
        expect(execution).to have_received(:update!).with(
          status: :failed,
          result: result.as_json,
          completed_at: be_within(1.second).of(Time.current)
        )
      end
    end

    context 'when command executes successfully' do
      before do
        allow(adapter).to receive(:supports_commands?).and_return(true)
        allow(adapter).to receive(:execute_command).with('test_command', {}).and_return(command_result)
      end

      it 'returns success result and marks execution as completed' do
        result = described_class.execute_for_device(device, 'test_command', {}, employee)

        expect(result.success).to be true
        expect(execution).to have_received(:update!).with(
          status: :completed,
          result: command_result.as_json,
          completed_at: be_within(1.second).of(Time.current)
        )
      end
    end

    context 'when command execution raises exception' do
      before do
        allow(adapter).to receive(:supports_commands?).and_return(true)
        allow(adapter).to receive(:execute_command).and_raise(StandardError, 'Connection failed')
      end

      it 'returns failure result and marks execution as failed' do
        result = described_class.execute_for_device(device, 'test_command', {}, employee)

        expect(result.success).to be false
        expect(result.error).to eq('Connection failed')
        expect(execution).to have_received(:update!).with(
          status: :failed,
          result: result.as_json,
          completed_at: be_within(1.second).of(Time.current)
        )
      end
    end
  end
end
