# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Attendance::DeviceCommand::ValidationConcern, type: :concern do
  let(:test_class) do
    Class.new do
      include Attendance::DeviceCommand::ValidationConcern
    end
  end

  let(:device) { create(:attendance_device) }
  let(:adapter) { double('adapter') }
  let(:command_class) { double('command_class') }
  let(:command_instance) { double('command_instance') }

  before do
    allow(device).to receive(:create_adapter).and_return(adapter)
  end

  describe '.validate_for_device' do
    context 'when device does not support commands' do
      before do
        allow(adapter).to receive(:supports_commands?).and_return(false)
      end

      it 'returns validation error for unsupported device' do
        result = test_class.validate_for_device(device, 'test_command', {})

        expect(result).to eq({
                               valid: false,
                               error: "Device does not support commands"
                             })
      end
    end

    context 'when command class is not found' do
      before do
        allow(adapter).to receive(:supports_commands?).and_return(true)
        allow(adapter).to receive(:get_command_class).with('unknown_command').and_return(nil)
        allow(device).to receive(:adapter_type).and_return('TestAdapter')
      end

      it 'returns validation error for unknown command' do
        result = test_class.validate_for_device(device, 'unknown_command', {})

        expect(result).to eq({
                               valid: false,
                               error: "Command 'unknown_command' not supported by TestAdapter adapter"
                             })
      end
    end

    context 'when command validation fails' do
      before do
        allow(adapter).to receive(:supports_commands?).and_return(true)
        allow(adapter).to receive(:get_command_class).with('test_command').and_return(command_class)
        allow(command_class).to receive(:new).with({ param: 'invalid' }).and_return(command_instance)
        allow(command_instance).to receive(:validation_result).and_return({
                                                                            valid: false,
                                                                            error: "Parameter 'param' is required"
                                                                          })
      end

      it 'returns command validation error' do
        result = test_class.validate_for_device(device, 'test_command', { param: 'invalid' })

        expect(result).to eq({
                               valid: false,
                               error: "Parameter 'param' is required"
                             })
      end
    end

    context 'when command validation succeeds' do
      before do
        allow(adapter).to receive(:supports_commands?).and_return(true)
        allow(adapter).to receive(:get_command_class).with('test_command').and_return(command_class)
        allow(command_class).to receive(:new).with({ param: 'valid' }).and_return(command_instance)
        allow(command_instance).to receive(:validation_result).and_return({ valid: true })
      end

      it 'returns successful validation' do
        result = test_class.validate_for_device(device, 'test_command', { param: 'valid' })

        expect(result).to eq({ valid: true })
      end
    end
  end
end
