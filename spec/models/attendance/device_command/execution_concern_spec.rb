# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Attendance::DeviceCommand::ExecutionConcern, type: :concern do
  let(:test_class) do
    Class.new do
      include Attendance::DeviceCommand::ExecutionConcern
    end
  end

  let(:device) { create(:attendance_device) }
  let(:employee) { create(:employee) }
  let(:adapter) { double('adapter') }
  let(:execution) { create(:attendance_command_execution, device: device) }
  let(:command_result) { Attendance::CommandResult.success('Command executed successfully') }

  before do
    allow(device).to receive(:create_adapter).and_return(adapter)
    allow(Attendance::CommandExecution).to receive(:create!).and_return(execution)
    allow(execution).to receive(:update!)
  end

  describe '.execute_for_device' do
    it 'creates execution record with correct attributes' do
      allow(adapter).to receive(:supports_commands?).and_return(true)
      allow(adapter).to receive(:execute_command).and_return(command_result)

      test_class.execute_for_device(device, 'test_command', { param: 'value' }, employee)

      expect(Attendance::CommandExecution).to have_received(:create!).with({
                                                                             device: device,
                                                                             command_name: 'test_command',
                                                                             parameters: { param: 'value' },
                                                                             status: :running,
                                                                             executed_by: employee,
                                                                             started_at: be_within(1.second).of(Time.current)
                                                                           })
    end

    context 'when device does not support commands' do
      before do
        allow(adapter).to receive(:supports_commands?).and_return(false)
      end

      it 'marks execution as failed and returns failure result' do
        result = test_class.execute_for_device(device, 'test_command', {}, employee)

        expect(result.success).to be false
        expect(result.error).to eq("Device does not support commands")
        expect(execution).to have_received(:update!).with({
                                                            status: :failed,
                                                            result: result.as_json,
                                                            completed_at: be_within(1.second).of(Time.current)
                                                          })
      end
    end

    context 'when command executes successfully' do
      before do
        allow(adapter).to receive(:supports_commands?).and_return(true)
        allow(adapter).to receive(:execute_command).with('test_command', {}).and_return(command_result)
      end

      it 'marks execution as completed and returns success result' do
        result = test_class.execute_for_device(device, 'test_command', {}, employee)

        expect(result).to eq(command_result)
        expect(execution).to have_received(:update!).with({
                                                            status: :completed,
                                                            result: command_result.as_json,
                                                            completed_at: be_within(1.second).of(Time.current)
                                                          })
      end
    end

    context 'when command execution fails' do
      let(:failed_result) { Attendance::CommandResult.failure('Command failed') }

      before do
        allow(adapter).to receive(:supports_commands?).and_return(true)
        allow(adapter).to receive(:execute_command).and_return(failed_result)
      end

      it 'marks execution as failed based on result' do
        result = test_class.execute_for_device(device, 'test_command', {}, employee)

        expect(result).to eq(failed_result)
        expect(execution).to have_received(:update!).with({
                                                            status: :failed,
                                                            result: failed_result.as_json,
                                                            completed_at: be_within(1.second).of(Time.current)
                                                          })
      end
    end

    context 'when command execution raises exception' do
      before do
        allow(adapter).to receive(:supports_commands?).and_return(true)
        allow(adapter).to receive(:execute_command).and_raise(StandardError, 'Connection timeout')
      end

      it 'catches exception and marks execution as failed' do
        result = test_class.execute_for_device(device, 'test_command', {}, employee)

        expect(result.success).to be false
        expect(result.error).to eq('Connection timeout')
        expect(execution).to have_received(:update!).with({
                                                            status: :failed,
                                                            result: result.as_json,
                                                            completed_at: be_within(1.second).of(Time.current)
                                                          })
      end
    end
  end
end
