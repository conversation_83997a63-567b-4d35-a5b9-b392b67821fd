require 'rails_helper'

RSpec.describe 'Settings API Integration', type: :request do
  let(:headers) { { 'Content-Type' => 'application/json' } }

  before do
    # Mock authentication and authorization for integration tests
    allow_any_instance_of(Api::SettingsController).to receive(:authenticate_session!).and_return(true)
    allow_any_instance_of(Api::SettingsController).to receive(:authorize!).and_return(true)
  end

  describe 'GET /api/settings' do
    let!(:attendance_setting) { create(:setting, namespace: 'attendance', key: 'work_start_time', value: '09:00') }
    let!(:system_setting) { create(:setting, namespace: 'system', key: 'app_name', value: 'AtharPeople') }

    it 'returns all settings' do
      get '/api/settings', headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json['data'].length).to be >= 2
    end

    it 'filters settings by namespace using Ransack' do
      get '/api/settings?filter[namespace_eq]=attendance', headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json['data'].all? { |s| s['attributes']['namespace'] == 'attendance' }).to be true
    end
  end

  describe 'GET /api/settings/:namespace/:key' do
    let!(:setting) { create(:setting, namespace: 'attendance', key: 'work_start_time', value: '09:00', description: 'Work start time') }

    it 'returns the specific setting' do
      get '/api/settings/attendance/work_start_time', headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      
      attributes = json['data']['attributes']
      expect(attributes['namespace']).to eq('attendance')
      expect(attributes['key']).to eq('work_start_time')
      expect(attributes['value']).to eq('09:00')
      expect(attributes['description']).to eq('Work start time')
    end

    it 'returns 404 for non-existent setting' do
      get '/api/settings/attendance/nonexistent', headers: headers

      expect(response).to have_http_status(:not_found)
      json = JSON.parse(response.body)
      expect(json['errors'][0]['detail']).to eq('Setting not found')
    end
  end

  describe 'PATCH /api/settings/:namespace/:key' do
    let!(:setting) { create(:setting, namespace: 'attendance', key: 'work_start_time', value: '09:00', is_editable: true) }

    it 'updates the setting' do
      patch '/api/settings/attendance/work_start_time', 
            params: { setting: { value: '08:30', description: 'Updated start time' } }.to_json,
            headers: headers

      expect(response).to have_http_status(:ok)
      
      setting.reload
      expect(setting.value).to eq('08:30')
      expect(setting.description).to eq('Updated start time')
    end

    it 'prevents updating non-editable settings' do
      setting.update!(is_editable: false)

      patch '/api/settings/attendance/work_start_time',
            params: { setting: { value: '08:30' } }.to_json,
            headers: headers

      expect(response).to have_http_status(:unprocessable_entity)
      json = JSON.parse(response.body)
      expect(json['errors'][0]['detail']).to eq('This setting cannot be edited')
    end
  end

  describe 'PUT /api/settings/:namespace/:key' do
    it 'creates a new setting when it does not exist' do
      expect {
        put '/api/settings/payroll/overtime_rate',
            params: { setting: { value: '1.5', description: 'Overtime rate' } }.to_json,
            headers: headers
      }.to change(Setting, :count).by(1)

      expect(response).to have_http_status(:created)
      
      json = JSON.parse(response.body)
      attributes = json['data']['attributes']
      expect(attributes['namespace']).to eq('payroll')
      expect(attributes['key']).to eq('overtime_rate')
      expect(attributes['value']).to eq('1.5')
    end

    it 'updates an existing setting' do
      setting = create(:setting, namespace: 'attendance', key: 'work_start_time', value: '09:00', is_editable: true)

      put '/api/settings/attendance/work_start_time',
          params: { setting: { value: '08:30', description: 'Updated time' } }.to_json,
          headers: headers

      expect(response).to have_http_status(:ok)
      
      setting.reload
      expect(setting.value).to eq('08:30')
      expect(setting.description).to eq('Updated time')
    end
  end

  describe 'DELETE /api/settings/:namespace/:key' do
    let!(:setting) { create(:setting, namespace: 'test', key: 'deletable_setting', value: 'test', is_editable: true) }

    it 'deletes the setting' do
      expect {
        delete '/api/settings/test/deletable_setting', headers: headers
      }.to change(Setting, :count).by(-1)

      expect(response).to have_http_status(:no_content)
    end

    it 'prevents deleting non-editable settings' do
      setting.update!(is_editable: false)

      delete '/api/settings/test/deletable_setting', headers: headers

      expect(response).to have_http_status(:unprocessable_entity)
      json = JSON.parse(response.body)
      expect(json['errors'][0]['detail']).to eq('This setting cannot be deleted')
    end
  end

  describe 'Business Logic Integration' do
    let!(:setting) { create(:setting, namespace: 'attendance', key: 'work_start_time', value: '09:00', is_editable: true) }

    it 'triggers attendance recalculation for attendance settings' do
      expect(Attendance::BatchPeriodCalculationWorker).to receive(:perform_async).with(
        30.days.ago.to_date.to_s,
        Date.today.to_s
      )

      patch '/api/settings/attendance/work_start_time',
            params: { setting: { value: '08:30' } }.to_json,
            headers: headers

      expect(response).to have_http_status(:ok)
    end

    it 'does not trigger recalculation for non-attendance settings' do
      system_setting = create(:setting, namespace: 'system', key: 'app_name', value: 'Test', is_editable: true)
      
      expect(Attendance::BatchPeriodCalculationWorker).not_to receive(:perform_async)

      patch '/api/settings/system/app_name',
            params: { setting: { value: 'Updated App' } }.to_json,
            headers: headers

      expect(response).to have_http_status(:ok)
    end
  end
end
