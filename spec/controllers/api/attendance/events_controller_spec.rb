require 'rails_helper'

RSpec.describe Api::Attendance::EventsController, type: :controller do
  let(:employee1) { create(:employee, department: :operations) }
  let(:employee2) { create(:employee, department: :hr) }
  let(:attendance_event1) { create(:attendance_event, employee: employee1) }
  let(:attendance_event2) { create(:attendance_event, employee: employee2) }

  before do
    # Mock authentication
    controller.stubs(:authenticate_session!).returns(true)
    controller.stubs(:authenticate_api_user!).returns(true)

    # Set up test data
    attendance_event1
    attendance_event2
  end

  describe "GET #index" do
    context "when user has full read permission" do
      before do
        controller.stubs(:can?).with(:read, :attendance_event).returns(true)
        controller.stubs(:can?).with(:read_own, :attendance_event).returns(false)
        controller.stubs(:can?).with(:manage, :attendance_event).returns(false)
        controller.stubs(:current_employee).returns(employee1)
      end

      it "does not apply employee filter" do
        get :index

        # Check that no employee filter was applied to params
        expect(controller.params[:filter]).to be_nil
        expect(response).to have_http_status(:ok)
      end
    end

    context "when user has read_own permission" do
      before do
        controller.stubs(:can?).with(:read, :attendance_event).returns(false)
        controller.stubs(:can?).with(:read_own, :attendance_event).returns(true)
        controller.stubs(:can?).with(:manage, :attendance_event).returns(false)
        controller.stubs(:current_employee).returns(employee1)
      end

      it "applies employee filter to params" do
        get :index

        # Check that employee filter was applied to params
        expect(controller.params[:filter][:employee_id_eq]).to eq(employee1.id)
        expect(response).to have_http_status(:ok)
      end
    end

    context "when user has no permissions" do
      before do
        controller.stubs(:can?).with(:read, :attendance_event).returns(false)
        controller.stubs(:can?).with(:read_own, :attendance_event).returns(false)
        controller.stubs(:can?).with(:manage, :attendance_event).returns(false)
        controller.stubs(:current_employee).returns(employee1)
      end

      it "applies empty result filter" do
        get :index

        # Check that empty result filter was applied
        expect(controller.params[:filter][:employee_id_eq]).to eq(-1)
        expect(response).to have_http_status(:forbidden)
      end
    end

    context "when filtering by activity_type_start" do
      let!(:regular_event) { create(:attendance_event, employee: employee1, activity_type: :regular) }
      let!(:break_event) { create(:attendance_event, employee: employee1, activity_type: :break) }
      let!(:lunch_event) { create(:attendance_event, employee: employee1, activity_type: :lunch) }

      before do
        controller.stubs(:can?).with(:read, :attendance_event).returns(true)
        controller.stubs(:can?).with(:read_own, :attendance_event).returns(false)
        controller.stubs(:can?).with(:manage, :attendance_event).returns(false)
        controller.stubs(:current_employee).returns(employee1)
      end

      it "handles activity_type_start filter with Arabic text without PostgreSQL error" do
        # This reproduces the exact error from the log:
        # filter[activity_type_start]=%D9%88%D9%82%D8%AA (which is "وقت" in Arabic)
        arabic_text = "وقت"

        expect {
          get :index, params: {
            filter: {
              activity_type_start: arabic_text,
              employee_id_eq: employee1.id
            }
          }
        }.not_to raise_error

        expect(response).to have_http_status(:ok)
      end

      it "handles activity_type_start filter with English text" do
        expect {
          get :index, params: {
            filter: {
              activity_type_start: "reg",
              employee_id_eq: employee1.id
            }
          }
        }.not_to raise_error

        expect(response).to have_http_status(:ok)
      end

      it "handles activity_type_eq filter with valid enum value" do
        get :index, params: {
          filter: {
            activity_type_eq: "regular",
            employee_id_eq: employee1.id
          }
        }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['data'].length).to eq(1)
        expect(json_response['data'][0]['attributes']['activity_type']).to eq('regular')
      end

      it "handles activity_type_cont filter for partial matching" do
        get :index, params: {
          filter: {
            activity_type_cont: "rea",
            employee_id_eq: employee1.id
          }
        }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['data'].length).to eq(1)
        expect(json_response['data'][0]['attributes']['activity_type']).to eq('regular')
      end

      it "returns empty results for non-matching filter" do
        get :index, params: {
          filter: {
            activity_type_start: "xyz",
            employee_id_eq: employee1.id
          }
        }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['data']).to be_empty
      end
    end
  end

  describe "GET #show" do
    context "when user has full read permission" do
      before do
        allow(controller).to receive(:can?).with(:read, :attendance_event).and_return(true)
        allow(controller).to receive(:can?).with(:read_own, :attendance_event).and_return(false)
        allow(controller).to receive(:can?).with(:manage, :attendance_event).and_return(false)
        allow(controller).to receive(:current_employee).and_return(employee1)
      end

      it "allows access to any attendance event" do
        get :show, params: { id: attendance_event2.id }
        expect(response).to have_http_status(:ok)
      end
    end

    context "when user has read_own permission" do
      before do
        allow(controller).to receive(:can?).with(:read, :attendance_event).and_return(false)
        allow(controller).to receive(:can?).with(:read_own, :attendance_event).and_return(true)
        allow(controller).to receive(:can?).with(:manage, :attendance_event).and_return(false)
        allow(controller).to receive(:current_employee).and_return(employee1)
      end

      it "allows access to own attendance event" do
        get :show, params: { id: attendance_event1.id }
        expect(response).to have_http_status(:ok)
      end

      it "denies access to other's attendance event" do
        get :show, params: { id: attendance_event2.id }
        expect(response).to have_http_status(:forbidden)

        json_response = JSON.parse(response.body)
        expect(json_response["errors"].first["detail"]).to eq("You are not allowed to read this attendance_event")
      end
    end

    context "when user has no permissions" do
      before do
        allow(controller).to receive(:can?).with(:read, :attendance_event).and_return(false)
        allow(controller).to receive(:can?).with(:read_own, :attendance_event).and_return(false)
        allow(controller).to receive(:can?).with(:manage, :attendance_event).and_return(false)
        allow(controller).to receive(:current_employee).and_return(employee1)
      end

      it "denies access to any attendance event" do
        get :show, params: { id: attendance_event1.id }
        expect(response).to have_http_status(:forbidden)

        json_response = JSON.parse(response.body)
        expect(json_response["errors"].first["detail"]).to eq("You are not allowed to read this attendance_event")
      end
    end
  end

  describe "#apply_permission_filters" do
    it "applies correct filters based on permissions" do
      # Test full read permission
      allow(controller).to receive(:can?).with(:read, :attendance_event).and_return(true)
      allow(controller).to receive(:can?).with(:manage, :attendance_event).and_return(false)
      allow(controller).to receive(:current_employee).and_return(employee1)

      controller.params.clear
      controller.send(:apply_permission_filters)
      expect(controller.params[:filter]).to be_nil

      # Test read_own permission
      allow(controller).to receive(:can?).with(:read, :attendance_event).and_return(false)
      allow(controller).to receive(:can?).with(:read_own, :attendance_event).and_return(true)

      controller.params.clear
      controller.send(:apply_permission_filters)
      expect(controller.params[:filter][:employee_id_eq]).to eq(employee1.id)

      # Test no permissions
      allow(controller).to receive(:can?).with(:read_own, :attendance_event).and_return(false)

      controller.params.clear
      controller.send(:apply_permission_filters)
      expect(controller.params[:filter][:employee_id_eq]).to eq(-1)
    end
  end

  describe "#is_own_event?" do
    before do
      allow(controller).to receive(:current_employee).and_return(employee1)
    end

    it "returns true for own event" do
      controller.instance_variable_set(:@attendance_event, attendance_event1)
      expect(controller.send(:is_own_event?)).to be true
    end

    it "returns false for other's event" do
      controller.instance_variable_set(:@attendance_event, attendance_event2)
      expect(controller.send(:is_own_event?)).to be false
    end
  end
end
