require 'rails_helper'

RSpec.describe Api::SettingsController, type: :controller do
  include ApiHelpers

  before do
    mock_authenticate_session!
  end

  describe 'GET #index' do
    let!(:attendance_setting1) { create(:setting, namespace: 'attendance', key: 'work_start_time', value: '09:00') }
    let!(:attendance_setting2) { create(:setting, namespace: 'attendance', key: 'work_end_time', value: '17:00') }
    let!(:system_setting) { create(:setting, namespace: 'system', key: 'app_name', value: 'AtharPeople') }

    context 'when user has read permission' do
      before { mock_authorize!(true) }

      it 'returns all settings when no namespace filter' do
        get :index

        expect(response).to have_http_status(:ok)
        data = json_response['data']
        expect(data.length).to eq(3)
      end

      it 'supports Ransack filtering by namespace' do
        get :index, params: { filter: { namespace_eq: 'attendance' } }

        expect(response).to have_http_status(:ok)
        data = json_response['data']
        expect(data.length).to eq(2)
        expect(data.map { |s| s['attributes']['namespace'] }).to all(eq('attendance'))
      end
    end

    context 'when user lacks read permission' do
      before { mock_authorize!(false) }

      it 'returns unauthorized' do
        get :index
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET #show' do
    let!(:setting) { create(:setting, namespace: 'attendance', key: 'work_start_time', value: '09:00') }

    context 'when user has read permission' do
      before { mock_authorize!(true) }

      it 'returns the setting when found' do
        get :show, params: { namespace: 'attendance', key: 'work_start_time' }

        expect(response).to have_http_status(:ok)
        data = json_response['data']
        expect(data['attributes']['namespace']).to eq('attendance')
        expect(data['attributes']['key']).to eq('work_start_time')
        expect(data['attributes']['value']).to eq('09:00')
      end

      it 'returns not found when setting does not exist' do
        get :show, params: { namespace: 'attendance', key: 'nonexistent' }

        expect(response).to have_http_status(:not_found)
        expect(json_response['errors'][0]['detail']).to eq('Setting not found')
      end
    end

    context 'when user lacks read permission' do
      before { mock_authorize!(false) }

      it 'returns unauthorized' do
        get :show, params: { namespace: 'attendance', key: 'work_start_time' }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'POST #create' do
    context 'when user has create permission' do
      before { mock_authorize!(true) }

      it 'creates a new setting with valid params' do
        setting_params = {
          setting: {
            namespace: 'payroll',
            key: 'overtime_rate',
            value: '1.5',
            description: 'Overtime multiplier rate',
            is_editable: true
          }
        }

        expect {
          post :create, params: setting_params
        }.to change(Setting, :count).by(1)

        expect(response).to have_http_status(:created)
        data = json_response['data']
        expect(data['attributes']['namespace']).to eq('payroll')
        expect(data['attributes']['key']).to eq('overtime_rate')
        expect(data['attributes']['value']).to eq('1.5')
      end

      it 'returns validation errors with invalid params' do
        setting_params = {
          setting: {
            namespace: '',
            key: 'test_key',
            value: 'test_value'
          }
        }

        post :create, params: setting_params

        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['errors']).to be_present
      end
    end

    context 'when user lacks create permission' do
      before { mock_authorize!(false) }

      it 'returns unauthorized' do
        post :create, params: { setting: { namespace: 'test', key: 'test', value: 'test' } }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'PATCH #update' do
    let!(:setting) { create(:setting, namespace: 'attendance', key: 'work_start_time', value: '09:00', is_editable: true) }

    context 'when user has update permission' do
      before { mock_authorize!(true) }

      it 'updates the setting with valid params' do
        update_params = {
          namespace: 'attendance',
          key: 'work_start_time',
          setting: {
            value: '08:30',
            description: 'Updated work start time'
          }
        }

        patch :update, params: update_params

        expect(response).to have_http_status(:ok)
        setting.reload
        expect(setting.value).to eq('08:30')
        expect(setting.description).to eq('Updated work start time')
      end

      it 'triggers attendance recalculation for attendance settings' do
        expect(Attendance::BatchPeriodCalculationWorker).to receive(:perform_async)

        patch :update, params: {
          namespace: 'attendance',
          key: 'work_start_time',
          setting: { value: '08:30' }
        }

        expect(response).to have_http_status(:ok)
      end

      it 'prevents updating non-editable settings' do
        setting.update!(is_editable: false)

        patch :update, params: {
          namespace: 'attendance',
          key: 'work_start_time',
          setting: { value: '08:30' }
        }

        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['errors'][0]['detail']).to eq('This setting cannot be edited')
      end

      it 'returns not found for non-existent setting' do
        patch :update, params: {
          namespace: 'attendance',
          key: 'nonexistent',
          setting: { value: 'test' }
        }

        expect(response).to have_http_status(:not_found)
      end
    end

    context 'when user lacks update permission' do
      before { mock_authorize!(false) }

      it 'returns unauthorized' do
        patch :update, params: {
          namespace: 'attendance',
          key: 'work_start_time',
          setting: { value: '08:30' }
        }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'PUT #create_or_update' do
    context 'when user has update permission' do
      before { mock_authorize!(true) }

      it 'creates a new setting when it does not exist' do
        expect {
          put :create_or_update, params: {
            namespace: 'payroll',
            key: 'overtime_rate',
            setting: {
              value: '1.5',
              description: 'Overtime multiplier rate'
            }
          }
        }.to change(Setting, :count).by(1)

        expect(response).to have_http_status(:created)
        data = json_response['data']
        expect(data['attributes']['namespace']).to eq('payroll')
        expect(data['attributes']['key']).to eq('overtime_rate')
        expect(data['attributes']['value']).to eq('1.5')
      end

      it 'updates an existing setting' do
        setting = create(:setting, namespace: 'attendance', key: 'work_start_time', value: '09:00', is_editable: true)

        put :create_or_update, params: {
          namespace: 'attendance',
          key: 'work_start_time',
          setting: {
            value: '08:30',
            description: 'Updated work start time'
          }
        }

        expect(response).to have_http_status(:ok)
        setting.reload
        expect(setting.value).to eq('08:30')
        expect(setting.description).to eq('Updated work start time')
      end

      it 'prevents updating non-editable existing settings' do
        setting = create(:setting, namespace: 'system', key: 'version', value: '1.0', is_editable: false)

        put :create_or_update, params: {
          namespace: 'system',
          key: 'version',
          setting: { value: '2.0' }
        }

        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['errors'][0]['detail']).to eq('This setting cannot be edited')
      end
    end

    context 'when user lacks update permission' do
      before { mock_authorize!(false) }

      it 'returns unauthorized' do
        put :create_or_update, params: {
          namespace: 'test',
          key: 'test',
          setting: { value: 'test' }
        }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'DELETE #destroy' do
    let!(:setting) { create(:setting, namespace: 'attendance', key: 'work_start_time', value: '09:00', is_editable: true) }

    context 'when user has destroy permission' do
      before { mock_authorize!(true) }

      it 'deletes the setting' do
        expect {
          delete :destroy, params: { namespace: 'attendance', key: 'work_start_time' }
        }.to change(Setting, :count).by(-1)

        expect(response).to have_http_status(:no_content)
      end

      it 'prevents deleting non-editable settings' do
        setting.update!(is_editable: false)

        delete :destroy, params: { namespace: 'attendance', key: 'work_start_time' }

        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['errors'][0]['detail']).to eq('This setting cannot be deleted')
      end

      it 'returns not found for non-existent setting' do
        delete :destroy, params: { namespace: 'attendance', key: 'nonexistent' }

        expect(response).to have_http_status(:not_found)
      end
    end

    context 'when user lacks destroy permission' do
      before { mock_authorize!(false) }

      it 'returns unauthorized' do
        delete :destroy, params: { namespace: 'attendance', key: 'work_start_time' }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
