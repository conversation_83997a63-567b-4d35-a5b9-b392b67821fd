require 'rails_helper'

RSpec.describe Api::EmployeesC<PERSON>roller, type: :controller do
  let(:user) { create(:user_with_role, role_name: 'admin') }

  before do
    # Mock authentication
    controller.stubs(:current_user).returns(user)
    controller.stubs(:authenticate_session!).returns(true)
    controller.stubs(:authorize!).returns(true)
  end

  describe 'POST #create' do
    let(:valid_attributes) do
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'password123',
        department: 'hr',
        start_date: Date.today,
        phone: '+962790000000'
      }
    end

    context 'with valid parameters' do
      it 'creates a new employee' do
        # Mock the RPC client to avoid actual RPC calls
        Employee.any_instance.stubs(:create_and_associate_user).returns(true)
        Employee.any_instance.stubs(:user_id).returns(1)

        expect {
          post :create, params: { employee: valid_attributes }
        }.to change(Employee, :count).by(1)

        expect(response).to have_http_status(:created)
      end

      it 'creates a new employee with user_roles_list' do
        # Mock the RPC client to avoid actual RPC calls
        Employee.any_instance.stubs(:create_and_associate_user).returns(true)
        Employee.any_instance.stubs(:user_id).returns(1)

        user_roles_list = [
          { role_id: '1', project_id: '1', is_default: true },
          { role_id: '2', project_id: '2' }
        ]

        post :create, params: { employee: valid_attributes.merge(user_roles_list: user_roles_list) }

        expect(response).to have_http_status(:created)
      end
    end

    context 'with invalid parameters' do
      it 'does not create a new employee' do
        # Invalid attributes (missing required fields)
        invalid_attributes = { name: 'John Doe' }

        expect {
          post :create, params: { employee: invalid_attributes }
        }.not_to change(Employee, :count)

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe 'PUT #update' do
    let!(:employee) { create(:employee) }

    before do
      # Mock the RPC client to avoid actual RPC calls
      Employee.any_instance.stubs(:update_user).returns(true)
    end

    it 'updates the employee with user_roles_list' do
      user_roles_list = [
        { role_id: '1', project_id: '1', is_default: true },
        { role_id: '2', project_id: '2' }
      ]

      put :update, params: { id: employee.id, employee: { user_roles_list: user_roles_list } }

      expect(response).to have_http_status(:ok)
    end
  end
end
