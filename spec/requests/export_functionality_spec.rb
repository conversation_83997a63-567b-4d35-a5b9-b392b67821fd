require 'rails_helper'

RSpec.describe "Export Functionality", type: :request do
  let(:user) { create(:user) }
  let(:employee) { create(:employee, user: user) }
  
  before do
    # Create some test employees
    3.times { create(:employee) }
    
    # Mock authentication
    allow_any_instance_of(ApplicationController).to receive(:authenticate_session!).and_return(true)
    allow_any_instance_of(ApplicationController).to receive(:current_user).and_return(user)
    allow_any_instance_of(ApplicationController).to receive(:current_employee).and_return(employee)
    allow_any_instance_of(ApplicationController).to receive(:authorize_read_all_or_own).and_return(true)
  end

  describe "GET /api/employees" do
    context "with CSV format" do
      it "returns CSV file" do
        get "/api/employees.csv"
        
        expect(response).to have_http_status(:ok)
        expect(response.content_type).to include("text/csv")
        expect(response.headers["Content-Disposition"]).to include("attachment")
        expect(response.headers["Content-Disposition"]).to include(".csv")
      end
    end

    context "with PDF format" do
      it "returns PDF file" do
        get "/api/employees.pdf"
        
        expect(response).to have_http_status(:ok)
        expect(response.content_type).to include("application/pdf")
        expect(response.headers["Content-Disposition"]).to include("attachment")
        expect(response.headers["Content-Disposition"]).to include(".pdf")
      end
    end

    context "with XLSX format" do
      it "returns XLSX file" do
        get "/api/employees.xlsx"
        
        expect(response).to have_http_status(:ok)
        expect(response.content_type).to include("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        expect(response.headers["Content-Disposition"]).to include("attachment")
        expect(response.headers["Content-Disposition"]).to include(".xlsx")
      end
    end

    context "with JSON format (default)" do
      it "returns JSON response" do
        get "/api/employees.json"
        
        expect(response).to have_http_status(:ok)
        expect(response.content_type).to include("application/vnd.api+json")
        expect(response.headers["Content-Disposition"]).to be_nil
      end
    end
  end
end
