require 'rails_helper'

RSpec.describe Salary::SlipService, type: :service do
  let(:employee) { create(:employee, name: '<PERSON>') }
  let(:salary_package) { create(:salary_package, employee: employee, base_salary: 3000) }
  let(:salary_calculation) do
    create(:salary_calculation,
           employee: employee,
           salary_package: salary_package,
           gross_salary: 3000,
           net_salary: 2700,
           status: :paid,
           deductions: { 'income_tax' => 200, 'social_security' => 100 })
  end

  describe '#generate' do
    context 'when salary calculation is paid' do
      it 'generates PDF using Grover HTML-to-PDF' do
        service = described_class.new(salary_calculation)

        expect {
          result = service.generate
          expect(result).to be_truthy
        }.to change { salary_calculation.salary_slip_pdf.attached? }.from(false).to(true)
      end

      it 'creates a valid PDF file' do
        service = described_class.new(salary_calculation)
        service.generate

        expect(salary_calculation.salary_slip_pdf).to be_attached
        expect(salary_calculation.salary_slip_pdf.content_type).to eq('application/pdf')
        expect(salary_calculation.salary_slip_pdf.byte_size).to be > 0
      end

      it 'generates HTML content with employee data' do
        service = described_class.new(salary_calculation)
        html_content = service.send(:generate_html_content)

        expect(html_content).to include(employee.name)
        expect(html_content).to include('salary-slip')
        expect(html_content).to include('ATHAR')
      end
    end

    context 'when salary calculation is not paid' do
      before { salary_calculation.update!(status: :approved) }

      it 'does not generate PDF' do
        service = described_class.new(salary_calculation)
        result = service.generate

        expect(result).to be_falsey
        expect(salary_calculation.salary_slip_pdf).not_to be_attached
      end
    end

    context 'when HTML generation fails' do
      before do
        allow(Grover).to receive(:new).and_raise(StandardError.new('Grover error'))
      end

      it 'raises an error' do
        service = described_class.new(salary_calculation)

        expect(Rails.logger).to receive(:error).with(/Failed to generate HTML PDF/)

        expect {
          service.generate
        }.to raise_error(StandardError, 'Grover error')
      end
    end
  end
end
