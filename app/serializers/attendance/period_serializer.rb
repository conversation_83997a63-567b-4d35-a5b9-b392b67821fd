module Attendance
  class PeriodSerializer
    include JSONAPI::Serializer

    set_type :attendance_period

    attributes :date, :period_type, :start_timestamp, :end_timestamp,
               :duration_minutes, :is_predicted, :created_at, :updated_at

    belongs_to :employee, serializer: EmployeeSerializer

    # Computed attributes
    attribute :start_time do |period|
      period.start_time&.iso8601
    end

    attribute :end_time do |period|
      period.end_time&.iso8601
    end

    attribute :formatted_start_time do |period|
      period.formatted_start_time
    end

    attribute :formatted_end_time do |period|
      period.formatted_end_time
    end

    attribute :formatted_duration do |period|
      period.formatted_duration
    end

    attribute :period_type_label do |period|
      case period.period_type
      when 'work'
        'Work Period'
      when 'break'
        'Break Period'
      when 'late'
        'Late Arrival'
      when 'early_departure'
        'Early Departure'
      when 'early_arrival'
        'Early Arrival'
      else
        period.period_type.humanize
      end
    end

    attribute :duration_hours do |period|
      (period.duration_minutes / 60.0).round(2)
    end

    attribute :status_badge do |period|
      if period.is_predicted
        { color: 'orange', text: 'Predicted' }
      else
        case period.period_type
        when 'work'
          { color: 'green', text: 'Work' }
        when 'break'
          { color: 'blue', text: 'Break' }
        when 'late'
          { color: 'red', text: 'Late' }
        when 'early_departure'
          { color: 'yellow', text: 'Early Out' }
        when 'early_arrival'
          { color: 'purple', text: 'Early In' }
        else
          { color: 'gray', text: period.period_type.humanize }
        end
      end
    end
  end
end
