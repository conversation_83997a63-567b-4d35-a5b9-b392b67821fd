module Api
  module Attendance
    class PeriodsController < ApplicationController
      include DateRangeFilterable
      include PeriodTypeFilterable

      before_action :authenticate_session!
      before_action :move_url_ids_to_event_params, only: [ :index ]
      before_action :set_employee, only: [ :daily_records, :month_statistics, :summary ]
      before_action :set_collection, only: [ :index ]

      api! "Lists attendance periods"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param :employee_id, Integer, desc: "Filter by employee ID (can also be specified in URL path)"
      param :date, Date, desc: "Filter by specific date"
      param :start_date, Date, desc: "Filter by date range (start)"
      param :end_date, Date, desc: "Filter by date range (end)"
      param :period_type, String, desc: "Filter by period type (work, break, late, early_departure)"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Lists attendance periods with filtering options.
        Requires permission: <code>:read, :attendance_event</code>.
      HTML
      )
      returns code: 200, desc: "List of attendance periods"

      def index
        return unless authorize!(:read, :attendance_event)

        # Apply filters directly in the action
        collection = @collection
        collection = apply_date_filters(collection, period_params)
        collection = apply_period_type_filter(collection, period_params[:period_type])

        apply_filters(collection) do |filtered_periods|
          records, meta = paginate(filtered_periods)
          serialize_response(records, meta: meta, serializer: ::Attendance::PeriodSerializer)
        end
      end

      api! "Gets daily attendance records for an employee"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, desc: "Employee ID (required if not in URL path)"
      param :date, Date, desc: "Date for daily records (defaults to today)"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Gets daily attendance records for an employee on a specific date.
        Includes arrival time, departure time, minutes spent working, and periods list.
        Requires permission: <code>:read, :attendance_event</code>.
      HTML
      )
      returns code: 200, desc: "Daily attendance records"

      def daily_records
        return unless authorize!(:read, :attendance_event)

        date = params[:date].presence || Date.today

        service = ::Attendance::DaySummaryService.new(@employee, date)
        daily_records = service.calculate_daily_records
        periods = service.get_periods

        meta = {
          date: date.to_s,
          summary: daily_records
        }

        serialize_response(periods, meta: meta, serializer: ::Attendance::PeriodSerializer)
      end

      api! "Gets monthly attendance statistics for an employee"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, desc: "Employee ID (required if not in URL path)"
      param :start_date, Date, desc: "Start date for statistics (defaults to beginning of current month)"
      param :end_date, Date, desc: "End date for statistics (defaults to today)"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Gets monthly attendance statistics for an employee.
        Includes leaves taken, late arrivals, early departures, and absences.
        Requires permission: <code>:read, :attendance_event</code>.
      HTML
      )
      returns code: 200, desc: "Monthly attendance statistics"

      def month_statistics
        return unless authorize!(:read, :attendance_event)

        start_date = params[:start_date].presence || Date.today.beginning_of_month
        end_date = params[:end_date].presence || Date.today

        # Use the service to calculate monthly statistics
        service = ::Attendance::StatisticsService.new(@employee, start_date, end_date)
        statistics = service.calculate_monthly_statistics

        # Prepare the response
        render json: {
          data: {
            employee_id: @employee.id,
            period: {
              start_date: start_date.to_s,
              end_date: end_date.to_s
            },
            statistics: statistics
          }
        }
      end

      api! "Recalculates attendance periods"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :recalculation, Hash, required: true, desc: "Recalculation parameters" do
        param :employee_id, Integer, desc: "Employee ID to recalculate periods for (optional)"
        param :date, Date, desc: "Specific date to recalculate (optional)"
        param :start_date, Date, desc: "Start date for range to recalculate (optional)"
        param :end_date, Date, desc: "End date for range to recalculate (optional)"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Triggers recalculation of attendance periods.
        Requires permission: <code>:manage, :attendance_event</code>.
      HTML
      )
      returns code: 200, desc: "Recalculation triggered successfully"

      def recalculate
        return unless authorize!(:manage, :attendance_event)

        recalc_params = recalculation_params

        if recalc_params[:date].present?
          # Use the date directly since it's already a Date object
          date = recalc_params[:date]

          if recalc_params[:employee_id].present?
            # Recalculate for specific employee and date
            ::Attendance::PeriodCalculationWorker.perform_async(
              recalc_params[:employee_id],
              date.to_s
            )
          else
            # Recalculate for all employees on this date
            ::Attendance::BatchPeriodCalculationWorker.perform_async(
              date.to_s,
              date.to_s
            )
          end
        elsif recalc_params[:start_date].present? && recalc_params[:end_date].present?
          # Convert dates to strings since they're already Date objects
          start_date = recalc_params[:start_date].to_s
          end_date = recalc_params[:end_date].to_s

          # Recalculate for date range
          ::Attendance::BatchPeriodCalculationWorker.perform_async(
            start_date,
            end_date,
            recalc_params[:employee_id]
          )
        else
          return serialize_errors({ detail: "Must provide either date or start_date and end_date." }, :unprocessable_entity)
        end

        render json: {
          message: "Period recalculation has been queued and will run in the background."
        }
      end

      api! "Gets attendance summary for an employee"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, desc: "Employee ID (required if not in URL path)"
      param :date, Date, desc: "Specific date for summary"
      param :start_date, Date, desc: "Start date for range summary"
      param :end_date, Date, desc: "End date for range summary"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Gets a summary of attendance periods for an employee.
        Requires permission: <code>:read, :attendance_event</code>.
      HTML
      )
      returns code: 200, desc: "Attendance summary"

      def summary
        return unless authorize!(:read, :attendance_event)

        # Get base collection for the employee
        collection = @employee.attendance_periods

        begin
          date_params = parse_date_params

          if date_params[:single_date]
            # Apply date filter for single date
            date = date_params[:date]
            periods = collection.where(date: date)
            summary_service = ::Attendance::SummaryService.new(periods)
            summary = summary_service.calculate

            # Count events for this employee and date
            event_count = ::Attendance::Event.where(employee: @employee)
                                             .for_date(date)
                                             .count

            # Include metadata
            render json: {
              date: date,
              summary: summary,
              meta: {
                period_count: periods.size,
                event_count: event_count
              }
            }
          else
            # Apply date range filter
            start_date = date_params[:start_date]
            end_date = date_params[:end_date]
            date_range = (start_date..end_date).to_a
            summaries = {}

            # Initialize counters for metadata
            total_period_count = 0
            total_event_count = 0

            # Calculate summary for each date in the range
            date_range.each do |date|
              periods = collection.where(date: date)
              summary_service = ::Attendance::SummaryService.new(periods)
              summaries[date.to_s] = summary_service.calculate

              # Count periods and events for this date
              period_count = periods.size
              event_count = ::Attendance::Event.where(employee: @employee)
                                               .for_date(date)
                                               .count

              total_period_count += period_count
              total_event_count += event_count
            end

            render json: {
              start_date: start_date,
              end_date: end_date,
              summaries: summaries,
              meta: {
                date_count: date_range.size,
                period_count: total_period_count,
                event_count: total_event_count
              }
            }
          end
        rescue InvalidDateFormatError
          serialize_errors({ detail: "Invalid date format" }, :unprocessable_entity)
        rescue StandardError => e
          serialize_errors({ detail: e.message }, :unprocessable_entity)
        end
      end

      private

      # Strong Parameters

      def period_params
        params.permit(:date, :start_date, :end_date, :period_type, :employee_id)
      end

      def recalculation_params
        params.require(:recalculation).permit(:employee_id, :date, :start_date, :end_date)
      end

      def set_employee
        @employee = ::Employee.find(params[:employee_id], params.dig(:attendance_period, :employee_id))
      end

      def set_collection
        @collection = if can?(:read, :attendance_event) || can?(:manage, :attendance_event)
                        ::Attendance::Period.all
                      elsif can?(:read_own, :attendance_event) && current_employee
                        ::Attendance::Period.where(employee_id: current_employee.id)
                      else
                        ::Attendance::Period.none
                      end
      end

      def move_url_ids_to_event_params
        if params[:employee_id].present?
          params[:attendance_period] ||= {}
          params[:attendance_period][:employee_id] = params[:employee_id]
        end
      end
    end
  end
end
