module Api
  module Attendance
    class EventsController < ApplicationController
      before_action :authenticate_session!
      before_action :move_url_ids_to_event_params, only: [ :create, :update ]
      before_action :apply_permission_filters, only: [ :index, :show, :update, :destroy ]
      before_action :set_collection, only: [ :index, :show, :update, :destroy, :undetermined ]
      before_action :set_attendance_event, only: [ :show, :update, :destroy ]
      before_action :authorize_read, only: [ :index ]
      before_action :authorize_read_specific, only: [ :show ]
      before_action :authorize_create, only: [ :create, :batch_create ]
      before_action :authorize_update, only: [ :update ]
      before_action :authorize_destroy, only: [ :destroy ]
      before_action :authorize_manage, only: [ :undetermined ]

      api! "Lists attendance events"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param :employee_id, Integer, desc: "Filter by employee ID"
      param :date, Date, desc: "Filter by specific date"
      param :start_date, Date, desc: "Filter by date range (start)"
      param :end_date, Date, desc: "Filter by date range (end)"
      param :event_type, String, desc: "Filter by event type (check_in, check_out, undetermined)"
      param :activity_type, String, desc: "Filter by activity type"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Lists attendance events with filtering options.
        — Users with <code>:read, :attendance_event</code> permission can see all events
        — Users with <code>:read_own, :attendance_event</code> permission can only see their own events
      HTML
      )
      returns code: 200, desc: "List of attendance events"

      def index
        apply_filters(@collection) do |filtered_and_sorted|
          records, meta = paginate(filtered_and_sorted)
          serialize_response(records, meta: meta, serializer: ::Attendance::EventSerializer)
        end
      end

      api! "Shows a specific attendance event"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the attendance event"
      param_group :include_params
      param_group :fields_params
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Shows details for a specific attendance event.
        — Users with <code>:read, :attendance_event</code> permission can see any event
        — Users with <code>:read_own, :attendance_event</code> permission can only see their own events
      HTML
      )
      returns code: 200, desc: "Attendance event details"
      error code: 404, desc: "Attendance event not found"

      def show
        serialize_response(@attendance_event, serializer: ::Attendance::EventSerializer)
      end

      api! "Creates a new attendance event"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :attendance_event, Hash, required: true, desc: "Attendance event attributes" do
        param :employee_id, Integer, required: true, desc: "ID of the employee"
        param :timestamp, Integer, required: true, desc: "Timestamp of the event"
        param :event_type, String, desc: "Type of event (check_in, check_out, undetermined)"
        param :activity_type, String, desc: "Type of activity (break, lunch, meeting, business_trip, work_from_home, remote, training, personal_errand)"
        param :location, String, desc: "Location"
        param :notes, String, desc: "Additional notes"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Creates a new attendance event.
        Requires permission: <code>:create, :attendance_event</code>.
      HTML
      )
      returns code: 201, desc: "Attendance event created successfully"
      error code: 422, desc: "Validation errors"

      def create
        @attendance_event = ::Attendance::Event.new(attendance_event_params)

        if @attendance_event.save
          # Update the daily summary
          date = @attendance_event.timestamp_date
          ::Attendance::Summary.recalculate_for(@attendance_event.employee, date)

          serialize_response(@attendance_event, status: :created, serializer: ::Attendance::EventSerializer)
        else
          serialize_errors(@attendance_event.errors)
        end
      end

      api! "Updates an attendance event"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the attendance event"
      param :attendance_event, Hash, required: true, desc: "Attendance event attributes" do
        param :timestamp, Integer, desc: "Timestamp of the event"
        param :event_type, String, desc: "Type of event (check_in, check_out, undetermined)"
        param :activity_type, String, desc: "Type of activity"
        param :location, String, desc: "Location"
        param :notes, String, desc: "Additional notes"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Updates an existing attendance event.
        Requires permission: <code>:update, :attendance_event</code>.
      HTML
      )
      returns code: 200, desc: "Attendance event updated successfully"
      error code: 422, desc: "Validation errors"

      def update
        old_date = @attendance_event.timestamp_date

        if @attendance_event.update(attendance_event_params)
          # Update the daily summary for both the old and new date if they differ
          new_date = @attendance_event.timestamp_date

          ::Attendance::Summary.recalculate_for(@attendance_event.employee, old_date)
          ::Attendance::Summary.recalculate_for(@attendance_event.employee, new_date) if old_date != new_date

          serialize_response(@attendance_event, serializer: ::Attendance::EventSerializer)
        else
          serialize_errors(@attendance_event.errors)
        end
      end

      api! "Deletes an attendance event"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the attendance event"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Deletes an attendance event by ID.
        Requires permission: <code>:destroy, :attendance_event</code>.
      HTML
      )
      returns code: 204, desc: "Attendance event deleted successfully"

      def destroy
        employee = @attendance_event.employee
        date = @attendance_event.timestamp_date

        @attendance_event.destroy!

        # Update the daily summary
        ::Attendance::Summary.recalculate_for(employee, date)

        head :no_content
      end

      api! "Batch create attendance events"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :batch_events, Hash, required: true, desc: "Batch event attributes" do
        param :events, Array, of: Hash, required: true, desc: "Array of event data" do
          param :employee_id, Integer, required: true, desc: "ID of the employee"
          param :timestamp, Integer, required: true, desc: "Timestamp of the event"
          param :event_type, String, desc: "Type of event (check_in, check_out, undetermined)"
          param :activity_type, String, desc: "Type of activity"
          param :location, String, desc: "Location"
          param :notes, String, desc: "Additional notes"
        end
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Creates multiple attendance events in a single request.
        Useful for importing data from attendance machines.
        Requires permission: <code>:create, :attendance_event</code>.
      HTML
      )
      returns code: 201, desc: "Batch events created successfully"
      error code: 422, desc: "Validation errors"

      def batch_create
        events_params = params.require(:batch_events).require(:events)
        results = { success: [], failure: [] }
        affected_summaries = {}

        events_params.each do |event_params|
          event = ::Attendance::Event.new(event_params.permit(
            :employee_id, :timestamp, :event_type, :activity_type, :location, :notes
          ))

          if event.save
            results[:success] << { id: event.id, employee_id: event.employee_id }

            # Track which summaries need to be updated
            date = event.timestamp_date
            affected_summaries[event.employee_id] ||= Set.new
            affected_summaries[event.employee_id] << date
          else
            results[:failure] << {
              employee_id: event_params[:employee_id],
              timestamp: event_params[:timestamp],
              errors: event.errors.full_messages
            }
          end
        end

        # Update all affected summaries
        affected_summaries.each do |employee_id, dates|
          employee = Employee.find(employee_id)
          dates.each do |date|
            ::Attendance::Summary.recalculate_for(employee, date)
          end
        end

        render json: results, status: :created
      end

      api! "Lists undetermined attendance events"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param :employee_id, Integer, desc: "Filter by employee ID"
      param :date, Date, desc: "Filter by specific date"
      param :start_date, Date, desc: "Filter by date range (start)"
      param :end_date, Date, desc: "Filter by date range (end)"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Lists attendance events with undetermined type for manual resolution.
        Requires permission: <code>:manage, :attendance_event</code>.
      HTML
      )
      returns code: 200, desc: "List of undetermined attendance events"

      def undetermined
        @collection = @collection.where(event_type: :undetermined)

        apply_filters(@collection) do |filtered_and_sorted|
          records, meta = paginate(filtered_and_sorted)
          serialize_response(records, meta: meta, serializer: ::Attendance::EventSerializer)
        end
      end

      api! "Records attendance for the current employee"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :attendance_event, Hash, required: true, desc: "Attendance event attributes" do
        param :event_type, String, required: true, desc: "Type of event (check_in, check_out)"
        param :activity_type, String, desc: "Type of activity (break, lunch, meeting, business_trip, work_from_home, remote, training, personal_errand)"
        param :location, String, desc: "Location"
        param :notes, String, desc: "Additional notes"
      end
      description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Records attendance for the current employee. This endpoint is specifically for employees
      to record their own attendance (clocking in/out). It automatically uses the current time
      and the employee associated with the authenticated user.
      Requires permission: <code>:record, :attendance_event</code>.
      HTML
      )
      returns code: 201, desc: "Attendance recorded successfully"
      error code: 422, desc: "Validation errors"

      def record
        unless current_employee
          return serialize_errors({ detail: "No employee record found for the current user." }, :unprocessable_entity)
        end

        # Create a new attendance event with the current timestamp and employee
        @attendance_event = AttendanceEvent.new(record_attendance_params)
        @attendance_event.employee = current_employee
        @attendance_event.timestamp = Time.current.to_i # Store as integer timestamp

        # Validate event_type is either check_in or check_out (not undetermined)
        unless [ "check_in", "check_out" ].include?(@attendance_event.event_type)
          return serialize_errors({ detail: "Event type must be 'check_in' or 'check_out' for self-recording." }, :unprocessable_entity)
        end

        if @attendance_event.save
          # Update the daily summary
          date = @attendance_event.timestamp_date
          Attendance::Summary.recalculate_for(@attendance_event.employee, date)

          serialize_response(@attendance_event, status: :created)
        else
          serialize_errors(@attendance_event.errors)
        end
      end

      private

      def set_attendance_event
        @attendance_event = ::Attendance::Event.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Attendance event not found" }, :not_found)
      end

      def attendance_event_params
        params.require(:attendance_event).permit(
          :employee_id, :timestamp, :event_type, :activity_type, :location, :notes
        )
      end

      def is_employee_route?
        request.path.include?('/employees/')
      end

      def record_attendance_params
        params.require(:attendance_event).permit(
          :event_type, :activity_type, :location, :notes
        )
      end

      def move_url_ids_to_event_params
        if params[:employee_id].present?
          params[:attendance_event][:employee_id] = params[:employee_id]
        end
      end

      def apply_permission_filters
        # Apply employee filter based on permissions by modifying params
        if can?(:read, :attendance_event) || can?(:manage, :attendance_event)
          # Full access - no filter needed
        elsif can?(:read_own, :attendance_event) && current_employee
          # Restrict to own records by adding employee_id filter to params
          params[:filter] ||= {}
          params[:filter][:employee_id_eq] = current_employee.id
        else
          # No access - filter to empty result
          params[:filter] ||= {}
          params[:filter][:employee_id_eq] = -1 # Non-existent employee ID
        end
      end

      def set_collection
        @collection = ::Attendance::Event.all
      end

      # Authorization methods
      def authorize_read
        unless can?(:read, :attendance_event) || can?(:read_own, :attendance_event) || can?(:manage, :attendance_event)
          render_forbidden("You don't have permission to view attendance events")
          false
        end
      end

      def authorize_read_specific
        return if can?(:read, :attendance_event) || can?(:manage, :attendance_event)
        return if can?(:read_own, :attendance_event) && is_own_event?

        render_forbidden("You are not allowed to read this attendance_event")
        false
      end

      def authorize_create
        authorize!(:create, :attendance_event)
      end

      def authorize_update
        authorize!(:update, :attendance_event)
      end

      def authorize_destroy
        authorize!(:destroy, :attendance_event)
      end

      def authorize_manage
        authorize!(:manage, :attendance_event)
      end

      def authorize_record
        authorize!(:record, :attendance_event)
      end

      def is_own_event?
        @attendance_event&.employee_id == current_employee&.id
      end

      def render_forbidden(message)
        serialize_errors({ detail: message }, :forbidden)
      end
    end
  end
end
