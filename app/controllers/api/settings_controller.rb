module Api
  class SettingsController < ApplicationController
    before_action :authenticate_session!
    before_action :set_setting_by_logical_key, only: [:show, :update, :destroy]

    api! "Lists settings"
    header "Authorization", "Scoped session token as Bearer token", required: true
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Lists settings with support for filtering, sorting, and pagination.
      Use <code>filter[namespace_eq]=attendance</code> to filter by namespace.
      Requires permission: <code>:read, :setting</code>.
    HTML
    )
    returns code: 200, desc: "List of settings"

    def index
      return unless authorize!(:read, :setting)

      apply_filters(Setting.all) do |filtered_settings|
        records, meta = paginate(filtered_settings)
        serialize_response(records, meta: meta)
      end
    end

    api! "Shows a specific setting"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :namespace, String, required: true, desc: "Setting namespace"
    param :key, String, required: true, desc: "Setting key"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Shows details for a specific setting by namespace and key.
      Requires permission: <code>:read, :setting</code>.
    HTML
    )
    returns code: 200, desc: "Setting details"

    def show
      return unless authorize!(:read, :setting)
      serialize_response(@setting)
    end

    api! "Creates a new setting"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :setting, Hash, required: true, desc: "Setting attributes" do
      param :namespace, String, required: true, desc: "Setting namespace"
      param :key, String, required: true, desc: "Setting key"
      param :value, String, required: true, desc: "Setting value"
      param :description, String, desc: "Setting description"
      param :is_editable, :boolean, desc: "Whether the setting can be edited"
    end
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new setting.
      Requires permission: <code>:create, :setting</code>.
    HTML
    )
    returns code: 201, desc: "Setting created successfully"

    def create
      return unless authorize!(:create, :setting)

      @setting = Setting.new(setting_params)

      if @setting.save
        serialize_response(@setting, status: :created)
      else
        serialize_errors(@setting.errors)
      end
    end

    api! "Updates a setting"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :namespace, String, required: true, desc: "Setting namespace"
    param :key, String, required: true, desc: "Setting key"
    param :setting, Hash, required: true, desc: "Setting attributes" do
      param :value, String, desc: "Setting value"
      param :description, String, desc: "Setting description"
      param :is_editable, :boolean, desc: "Whether the setting can be edited"
    end
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing setting by namespace and key.
      Requires permission: <code>:update, :setting</code>.
      Note: Only editable settings can be updated.
    HTML
    )
    returns code: 200, desc: "Setting updated successfully"

    def update
      return unless authorize!(:update, :setting)

      unless @setting.is_editable
        return serialize_errors({ detail: "This setting cannot be edited" }, :unprocessable_entity)
      end

      if @setting.update(setting_update_params)
        trigger_attendance_recalculation if attendance_setting?
        serialize_response(@setting)
      else
        serialize_errors(@setting.errors)
      end
    end

    api! "Creates or updates a setting"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :namespace, String, required: true, desc: "Setting namespace"
    param :key, String, required: true, desc: "Setting key"
    param :setting, Hash, required: true, desc: "Setting attributes" do
      param :value, String, required: true, desc: "Setting value"
      param :description, String, desc: "Setting description"
      param :is_editable, :boolean, desc: "Whether the setting can be edited"
    end
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new setting or updates an existing one by namespace and key.
      Requires permission: <code>:update, :setting</code>.
      Note: Only editable settings can be updated if they already exist.
    HTML
    )
    returns code: 200, desc: "Setting created or updated successfully"

    def create_or_update
      return unless authorize!(:update, :setting)

      @setting = Setting.find_or_initialize_by(
        namespace: params[:namespace],
        key: params[:key]
      )

      if @setting.persisted? && !@setting.is_editable
        return serialize_errors({ detail: "This setting cannot be edited" }, :unprocessable_entity)
      end

      if @setting.update(setting_create_or_update_params)
        trigger_attendance_recalculation if attendance_setting?
        serialize_response(@setting, status: @setting.previously_new_record? ? :created : :ok)
      else
        serialize_errors(@setting.errors)
      end
    end

    api! "Deletes a setting"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :namespace, String, required: true, desc: "Setting namespace"
    param :key, String, required: true, desc: "Setting key"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Deletes a setting by namespace and key.
      Requires permission: <code>:destroy, :setting</code>.
      Note: Only editable settings can be deleted.
    HTML
    )
    returns code: 204, desc: "Setting deleted successfully"

    def destroy
      return unless authorize!(:destroy, :setting)

      unless @setting.is_editable
        return serialize_errors({ detail: "This setting cannot be deleted" }, :unprocessable_entity)
      end

      @setting.destroy
      head :no_content
    end

    private

    def set_setting_by_logical_key
      @setting = Setting.find_by_logical_key_safe(params[:namespace], params[:key])
      unless @setting
        serialize_errors({ detail: "Setting not found" }, :not_found)
        return false
      end
    end

    def setting_params
      params.require(:setting).permit(:namespace, :key, :value, :description, :is_editable)
    end

    def setting_update_params
      params.require(:setting).permit(:value, :description, :is_editable)
    end

    def setting_create_or_update_params
      params.require(:setting).permit(:value, :description, :is_editable)
    end

    def attendance_setting?
      params[:namespace] == 'attendance' &&
        %w[work_start_time work_end_time duplicate_threshold_seconds].include?(params[:key])
    end

    def trigger_attendance_recalculation
      Attendance::BatchPeriodCalculationWorker.perform_async(
        30.days.ago.to_date.to_s,
        Date.today.to_s
      )
    end
  end
end
