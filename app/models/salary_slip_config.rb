# frozen_string_literal: true

class SalarySlipConfig < Athar::Commons::ActiveStruct::Base
  # Static styling configuration
  attribute :currency_code, :string, default: 'JOD'
  attribute :currency_symbol, :string, default: 'JOD'
  attribute :currency_position, :string, default: 'after'
  attribute :primary_color, :string, default: '#2C3E50'
  attribute :secondary_color, :string, default: '#7F8C8D'
  attribute :font_family, :string, default: 'Helvetica'
  attribute :page_size, :string, default: 'A4'
  attribute :page_layout, :string, default: 'portrait'

  # HTML-to-PDF specific options
  attribute :template_variant, :string, default: 'default'

  # High-resolution PDF options
  attribute :pdf_quality, :string, default: 'high'
  attribute :pdf_scale, :float, default: 1.0
  attribute :pdf_print_background, :boolean, default: true

  def self.current
    return @current if @current

    begin
      config_hash = Rails.application.config_for(:salary_slip)
      @current = new
      config_hash.each do |key, value|
        @current.send("#{key}=", value) if @current.respond_to?("#{key}=")
      end
      @current
    rescue
      @current = new # Use defaults if config file doesn't exist
    end
  end

  def self.reset!
    @current = nil
  end

  # Dynamic company data from Settings
  def company_name
    Setting.get('company', 'name', 'ATHAR')
  end

  def company_tagline
    Setting.get('company', 'tagline', '')
  end

  def company_address
    Setting.get('company', 'address', '789/1 Sector-2c, 38200 Makkah, Saudi Arabia')
  end

  def company_phone
    Setting.get('company', 'phone', '966848172194')
  end

  def company_email
    Setting.get('company', 'email', '<EMAIL>')
  end

  def company_logo_path
    Setting.get('company', 'logo_path', 'app/assets/images/slip_athar_logo.png')
  end

  def format_currency(amount)
    formatted = sprintf("%.2f", amount.to_f)
    case currency_position
    when 'before'
      "#{currency_symbol}#{formatted}"
    when 'after'
      "#{formatted} #{currency_symbol}"
    else
      "#{currency_symbol} #{formatted}"
    end
  end

  # Grover-specific PDF options
  def grover_options
    {
      format: page_size,
      margin: {
        top: '0.5in',
        bottom: '0.5in',
        left: '0.5in',
        right: '0.5in'
      },
      print_background: pdf_print_background,
      prefer_css_page_size: true,
      display_header_footer: false,
      timeout: 30_000,
      wait_until: 'networkidle0',
      # High-resolution settings
      scale: pdf_scale,
      device_scale_factor: 1.5,
      # Quality settings
      quality: pdf_quality == 'high' ? 100 : 75,
      # Additional Chrome flags for better rendering
      chrome_flags: [
        '--force-device-scale-factor=1.5',
        '--high-dpi-support=1',
        '--force-color-profile=srgb'
      ]
    }
  end

  # Chrome URL for Grover
  def chrome_url
    ENV['CHROME_URL'] || 'http://people-chrome:3000'
  end

  # Template path based on variant
  def template_path
    case template_variant
    when 'compact'
      'salary_slips/slip_compact'
    when 'detailed'
      'salary_slips/slip_detailed'
    else
      'salary_slips/slip'
    end
  end

end
