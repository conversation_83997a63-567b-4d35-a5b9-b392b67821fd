module Salary
  module AttendanceIntegration
    extend ActiveSupport::Concern
    
    def calculate_attendance_deductions(calculation)
      # Get period dates
      start_date = calculation.period_start_date
      end_date = calculation.period_end_date
      gross_salary = calculation.gross_salary

      # Get attendance summaries for the period
      summaries = employee.attendance_summaries.where(date: start_date..end_date)

      # Calculate working days in the month (excluding weekends)
      working_days = calculate_working_days(start_date, end_date)
      return 0 if working_days == 0 # Avoid division by zero

      # Count days with attendance
      days_with_attendance = summaries.where.not(first_check_in: nil).count

      # Calculate missed days (excluding approved leaves)
      approved_leave_days = count_approved_leave_days(start_date, end_date)
      missed_days = working_days - days_with_attendance - approved_leave_days
      missed_days = 0 if missed_days < 0 # Ensure we don't have negative missed days

      # Calculate daily rate
      daily_rate = gross_salary / working_days

      # Calculate deduction for missed days
      missed_days_deduction = daily_rate * missed_days
      
      # Track missed days deduction
      if missed_days > 0
        calculation.calculation_details.build(
          detail_type: 'deduction',
          category: 'attendance_missed',
          amount: missed_days_deduction,
          description: "Deduction for #{missed_days} missed work days"
        )
      end

      # Calculate late arrivals deduction
      late_arrivals_deduction = calculate_late_arrivals_deduction(start_date, end_date, gross_salary)
      
      # Track late arrivals deduction
      if late_arrivals_deduction > 0
        # Get late periods for additional details
        late_periods = employee.attendance_periods
                              .where(date: start_date..end_date)
                              .where(period_type: 'late')
        
        total_late_minutes = late_periods.sum(:duration_minutes)
        
        calculation.calculation_details.build(
          detail_type: 'deduction',
          category: 'attendance_late',
          amount: late_arrivals_deduction,
          description: "Deduction for late arrivals (#{total_late_minutes} minutes total)"
        )
      end

      # Calculate early departures deduction
      early_departures_deduction = calculate_early_departures_deduction(start_date, end_date, gross_salary)
      
      # Track early departures deduction
      if early_departures_deduction > 0
        # Get early departure periods for additional details
        early_departure_periods = employee.attendance_periods
                                         .where(date: start_date..end_date)
                                         .where(period_type: 'early_departure')
        
        total_early_minutes = early_departure_periods.sum(:duration_minutes)
        
        calculation.calculation_details.build(
          detail_type: 'deduction',
          category: 'attendance_early',
          amount: early_departures_deduction,
          description: "Deduction for early departures (#{total_early_minutes} minutes total)"
        )
      end

      # Total attendance deduction
      missed_days_deduction + late_arrivals_deduction + early_departures_deduction
    end
  end
end
