# frozen_string_literal: true

module SalarySlipHelper
  # Format currency according to configuration
  def format_currency(amount)
    return "0.00 #{@config.currency_symbol}" if amount.nil? || amount == 0

    formatted = sprintf("%.2f", amount.to_f)
    case @config.currency_position
    when 'before'
      "#{@config.currency_symbol}#{formatted}"
    when 'after'
      "#{formatted} #{@config.currency_symbol}"
    else
      "#{@config.currency_symbol} #{formatted}"
    end
  end

  # Format salary month from period string
  def format_salary_month(period)
    return "Unknown Period" if period.blank?

    # Handle different period formats: YYYY-MM or YYYY-MM-DD_YYYY-MM-DD
    if period.include?('_')
      # Custom period format
      start_date, end_date = period.split('_')
      start_parsed = Date.parse(start_date) rescue nil
      end_parsed = Date.parse(end_date) rescue nil

      if start_parsed && end_parsed
        if start_parsed.month == end_parsed.month && start_parsed.year == end_parsed.year
          return start_parsed.strftime('%B, %Y')
        else
          return "#{start_parsed.strftime('%b %Y')} - #{end_parsed.strftime('%b %Y')}"
        end
      end
    else
      # Standard YYYY-MM format
      begin
        date = Date.parse("#{period}-01")
        return date.strftime('%B, %Y')
      rescue
        # Fallback for invalid date
      end
    end

    period
  end

  # Calculate total taxes from deductions
  def calculate_total_taxes
    return 0 unless @salary_calculation.deductions.present?

    tax_types = [ 'income_tax', 'tax', 'taxes' ]
    total = 0

    @salary_calculation.deductions.each do |deduction_type, amount|
      if tax_types.any? { |tax_type| deduction_type.to_s.downcase.include?(tax_type) }
        total += amount.to_f
      end
    end

    total
  end

  # Get description for deduction types
  def deduction_description(deduction_type)
    descriptions = {
      'income_tax' => 'Income tax deduction as per government regulations',
      'social_security' => 'Social security contribution',
      'attendance' => 'Deduction for attendance-related issues',
      'leave' => 'Deduction for unpaid leave days',
      'other' => 'Other miscellaneous deductions'
    }

    descriptions[deduction_type.to_s] || "#{deduction_type.humanize} deduction"
  end

  # Get company logo URL or placeholder
  def company_logo_url
    # Try to get logo from configuration
    logo_path = @config.company_logo_path

    if logo_path.present? && File.exist?(Rails.root.join('public', logo_path))
      asset_url(logo_path)
    else
      # Return placeholder or default logo
      nil
    end
  end

  # Format employee department with fallback
  def employee_department_display
    @employee.department_name.presence ||
      @employee.department.presence ||
      'Employee'
  end

  # Format contact information
  def format_contact_info
    info_parts = []

    info_parts << @config.company_address if @config.company_address.present?

    contact_line = []
    contact_line << @config.company_phone if @config.company_phone.present?
    contact_line << @config.company_email if @config.company_email.present?
    info_parts << contact_line.join(' | ') if contact_line.any?

    info_parts << Date.current.strftime('%d/%m/%Y')

    info_parts
  end

  # Check if allowance should be displayed
  def display_allowance?(allowance_amount)
    allowance_amount.present? && allowance_amount.to_f > 0
  end

  # Get salary calculation summary
  def salary_summary
    {
      gross_salary: @salary_calculation.gross_salary,
      total_deductions: @salary_calculation.total_deductions,
      net_salary: @salary_calculation.net_salary,
      total_taxes: calculate_total_taxes
    }
  end

  # Format period dates for display
  def format_period_dates
    start_date = @salary_calculation.period_start_date
    end_date = @salary_calculation.period_end_date

    return "Unknown Period" unless start_date && end_date

    if start_date.month == end_date.month && start_date.year == end_date.year
      "#{start_date.strftime('%B %Y')}"
    else
      "#{start_date.strftime('%d %b %Y')} - #{end_date.strftime('%d %b %Y')}"
    end
  end

  # Format employee contact information
  def employee_contact_display
    contact_parts = []

    if @employee.email.present?
      contact_parts << @employee.email
    end

    if @employee.phone.present?
      contact_parts << @employee.phone
    end

    if @employee.start_date.present?
      contact_parts << "Start Date: #{@employee.start_date.strftime('%d/%m/%Y')}"
    end

    contact_parts.presence || ['No contact information available']
  end

  # Get company industry from settings
  def company_industry
    Setting.get('company', 'industry', 'Software Development')
  end

  # Get default payment method from settings
  def default_payment_method
    Setting.get('payroll', 'default_payment_method', 'Bank Transfer')
  end

  # Format employee job title with department
  def employee_job_title
    department = @employee.department_name.presence || 'Employee'
    job_title = Setting.get('employee', "job_title_#{@employee.id}", nil)

    if job_title.present?
      "#{job_title} - #{department}"
    else
      department
    end
  end
end
