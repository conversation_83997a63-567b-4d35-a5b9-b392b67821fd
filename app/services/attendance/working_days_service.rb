module Attendance
  class WorkingDaysService
    # Main public interface
    def self.working_days_for_employee(employee, start_date, end_date)
      (start_date..end_date).count do |date|
        is_working_day_for_employee?(employee, date)
      end
    end

    def self.is_working_day_for_employee?(employee, date)
      # 1. Check if it's a weekend (Friday/Saturday based on current logic)
      return false if weekend?(date)

      # 2. Check if employee was employed on this date
      return false unless employee_active_on_date?(employee, date)

      # 3. Check for company-wide attendance exemptions
      return false if company_attendance_exemption_exists?(date)

      # Note: Employee leaves are handled separately in calculations
      true
    end

    # Company-wide exemptions only (for salary calculations)
    def self.company_working_days(start_date, end_date)
      (start_date..end_date).count do |date|
        !weekend?(date) && !company_attendance_exemption_exists?(date)
      end
    end

    # Check if a specific date is a working day (company-wide)
    def self.is_working_day?(date)
      !weekend?(date) && !company_attendance_exemption_exists?(date)
    end

    # Get all non-working dates in a range (weekends + exemptions)
    def self.non_working_dates(start_date, end_date)
      (start_date..end_date).select do |date|
        weekend?(date) || company_attendance_exemption_exists?(date)
      end
    end

    # Get all working dates in a range
    def self.working_dates(start_date, end_date)
      (start_date..end_date).select do |date|
        is_working_day?(date)
      end
    end

    private

    def self.weekend?(date)
      # Friday = 5, Saturday = 6 (based on current system logic)
      [ 5, 6 ].include?(date.wday)
    end

    def self.employee_active_on_date?(employee, date)
      return false if date < employee.start_date
      return false if employee.inactive? # Based on existing status enum
      # Add end_date check when that field is added to employee model
      true
    end

    def self.company_attendance_exemption_exists?(date)
      # Use a class variable for caching within the same request
      @exemption_cache ||= {}
      cache_key = date.to_s

      return @exemption_cache[cache_key] if @exemption_cache.key?(cache_key)

      @exemption_cache[cache_key] = ::Attendance::Exemption.active
                                                           .affecting_attendance
                                                           .for_date(date)
                                                           .exists?
    end

    # Clear cache (useful for testing or long-running processes)
    def self.clear_cache!
      @exemption_cache = {}
    end
  end
end
