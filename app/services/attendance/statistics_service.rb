module Attendance
  class StatisticsService
    attr_reader :employee, :start_date, :end_date

    def initialize(employee, start_date = nil, end_date = nil)
      @employee = employee
      @start_date = start_date || Date.today.beginning_of_month
      @end_date = end_date || Date.today
    end

    def calculate_monthly_statistics
      {
        leaves_taken: leaves_taken_count,
        arrived_late: late_arrivals_count,
        departed_early: early_departures_count,
        absent: absences_count
      }
    end

    private

    def leaves_taken_count
      Leave.where(employee: employee)
           .where("start_date >= ? AND end_date <= ?", start_date, end_date)
           .where(status: [ :approved, :pending ])
           .count
    end

    def late_arrivals_count
      Attendance::Period.where(employee: employee)
                      .where(date: start_date..end_date)
                      .where(period_type: Attendance::Period::PERIOD_TYPES[:late])
                      .count
    end

    def early_departures_count
      Attendance::Period.where(employee: employee)
                      .where(date: start_date..end_date)
                      .where(period_type: Attendance::Period::PERIOD_TYPES[:early_departure])
                      .count
    end

    def absences_count
      absences = 0
      (start_date..end_date).each do |workday|
        # Skip weekends
        next if [ 0, 6 ].include?(workday.wday) # Skip Saturday and Sunday

        # Check if there's any attendance record for this day
        day_periods = Attendance::Period.where(employee: employee, date: workday)
        if day_periods.empty?
          # No periods recorded for this workday
          absences += 1
        end
      end
      absences
    end
  end
end
