module Salary
  class SlipService
    attr_reader :salary_calculation, :config

    def initialize(salary_calculation, options = {})
      @salary_calculation = salary_calculation
      @config = SalarySlipConfig.current
    end

    def generate
      return false unless salary_calculation.paid?

      # Use HTML-to-PDF generation with Grover
      pdf_content = generate_html_pdf

      # Attach PDF to salary calculation
      attach_pdf(pdf_content)
    end

    private

    # HTML-to-PDF generation using Grove<PERSON>
    def generate_html_pdf
      require 'grover'

      # Generate HTML content directly for now (until template rendering is fixed)
      html_content = generate_html_content

      # Fallback: try template rendering if direct generation fails
      if html_content.blank? || html_content.length < 100
        html_content = render_template_content
      end

      # Convert HTML to PDF using Grover with Chrome headless service
      chrome_ws_endpoint = get_chrome_websocket_endpoint

      # Configure Grover to use the remote Chrome service
      Grover.configure do |grover_config|
        grover_config.options = config.grover_options.merge(
          browser_ws_endpoint: chrome_ws_endpoint
        )
      end

      grover = Grover.new(html_content)
      grover.to_pdf
    rescue => e
      Rails.logger.error "Failed to generate HTML PDF: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise e
    end

    def attach_pdf(content)
      # Create a StringIO object instead of a temporary file
      pdf_io = StringIO.new(content)

      # Attach the PDF to the salary calculation
      salary_calculation.salary_slip_pdf.attach(
        io: pdf_io,
        filename: "salary_slip_#{salary_calculation.employee.name.parameterize}_#{salary_calculation.period}.pdf",
        content_type: 'application/pdf'
      )

      true
    rescue => e
      Rails.logger.error "Failed to attach PDF: #{e.message}"
      false
    end

    # Generate HTML content directly (temporary solution)
    def generate_html_content
      employee = salary_calculation.employee

      # Include helper methods
      extend SalarySlipHelper
      @config = config
      @salary_calculation = salary_calculation
      @employee = employee

      <<~HTML
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Salary Slip - #{employee.name}</title>
          <style>
            #{render_styles}
          </style>
        </head>
        <body>
          <div class="salary-slip">
            #{render_header_section}
            <div class="slip-content">
              #{render_employee_details_section}
              #{render_salary_table_section}
              #{render_totals_section}
              #{render_footer_section}
            </div>
          </div>
        </body>
        </html>
      HTML
    end

    # Fallback template rendering method
    def render_template_content
      begin
        controller = Api::Finance::SalaryCalculationsController.new
        controller.request = ActionDispatch::Request.new({})
        controller.response = ActionDispatch::Response.new

        controller.instance_variable_set(:@salary_calculation, salary_calculation)
        controller.instance_variable_set(:@employee, salary_calculation.employee)
        controller.instance_variable_set(:@config, config)

        controller.render_to_string(
          template: 'salary_slips/slip',
          layout: false
        )
      rescue => e
        Rails.logger.error "Template rendering failed: #{e.message}"
        ""
      end
    end

    # Get Chrome WebSocket endpoint from the headless service
    def get_chrome_websocket_endpoint
      require 'net/http'
      require 'json'

      begin
        uri = URI('http://people-chrome:3000/json')
        response = Net::HTTP.get_response(uri)

        if response.code == '200'
          endpoints = JSON.parse(response.body)
          endpoints.first&.dig('webSocketDebuggerUrl') if endpoints.any?
        end
      rescue => e
        Rails.logger.error "Failed to get Chrome WebSocket endpoint: #{e.message}"
        nil
      end
    end

    # Get Chrome WebSocket endpoint from the headless service
    def get_chrome_websocket_endpoint
      # Use the base WebSocket endpoint for the Chrome service
      'ws://people-chrome:3000'
    end

    # HTML rendering helper methods
    def render_styles
      <<~CSS
        * { margin: 0; padding: 0; box-sizing: border-box; }

        @page {
          margin: 0.3in 0.1in;
        }

        body {
          font-family: Arial, Helvetica, sans-serif;
          font-size: 10px;
          line-height: 1.3;
          color: #333843;
          background: white;
          margin: 0;
          padding: 5px;
        }

        .salary-slip {
          width: 100%;
          max-width: 1000px;
          margin: 0 auto;
          background: white;
        }

        /* Header Section - Above the border */
        .slip-header {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-bottom: 16px;
          padding: 16px 16px 0 16px;
          gap: 24px;
        }

        /* Main Content - With border */
        .slip-content {
          border: 1px solid #F0F1F3;
          border-radius: 12px;
          padding: 16px;
          margin: 0 16px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .logo-container {
          width: 120px;
          height: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
        }

        .logo-container svg {
          width: 120px;
          height: 120px;
          max-width: 120px;
          max-height: 120px;
        }

        .logo-container img {
          width: 120px;
          height: 120px;
          max-width: 120px;
          max-height: 120px;
          object-fit: contain;
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;
          image-rendering: pixelated;
          -ms-interpolation-mode: nearest-neighbor;
        }

        .logo-placeholder {
          width: 80px;
          height: 80px;
          background: #f8f9fa;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 32px;
          font-weight: bold;
          color: #333843;
        }

        .header-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .company-name {
          font-size: 14px;
          font-weight: bold;
          color: #333843;
          margin: 0 0 8px 0;
          letter-spacing: 1px;
        }

        .company-info {
          font-size: 12px;
          color: #667085;
          line-height: 1.6;
        }

        .company-info p {
          margin: 0 0 4px 0;
          font-size: 11px;
          color: #6B7280;
        }

        .total-amount-header {
          text-align: right;
          margin-left: auto;
        }

        .total-label {
          font-size: 11px;
          color: #6B7280;
          margin-bottom: 4px;
        }

        .total-value {
          font-size: 18px;
          font-weight: bold;
          color: #333843;
        }

        .total-amount-section {
          margin-top: 16px;
        }

        .total-label {
          font-size: 12px;
          color: #667085;
          margin-bottom: 4px;
        }

        .total-value {
          font-size: 24px;
          font-weight: bold;
          color: #333843;
        }

        /* Employee Details Section */
        .employee-details {
          display: flex;
          justify-content: space-between;
          margin-bottom: 32px;
          gap: 32px;
        }

        .details-left {
          flex: 1;
          border-radius: 8px;
          padding: 20px;
          background-color: #FAFAFA;
        }

        .details-right {
          flex: 1;
          padding: 20px;
          text-align: left;
        }

        .detail-item {
          margin-bottom: 16px;
        }

        .detail-label {
          font-size: 12px;
          color: #667085;
          margin-bottom: 4px;
        }

        .detail-value {
          font-size: 12px;
          color: #333843;
          font-weight: 500;
        }

        .employee-info {
          text-align: left;
        }

        .job-title {
          font-size: 12px;
          color: #667085;
          margin-bottom: 4px;
        }

        .employee-name {
          font-size: 14px;
          font-weight: bold;
          color: #333843;
          margin-bottom: 16px;
        }

        .contact-info {
          font-size: 12px;
          color: #667085;
          line-height: 1.5;
        }

        .note-section {
          margin-top: 16px;
        }

        .note-label {
          font-size: 12px;
          color: #667085;
          margin-bottom: 4px;
        }

        .note-text {
          font-size: 12px;
          color: #333843;
          line-height: 1.4;
        }

        /* Salary Table */
        .salary-table-section {
          margin-bottom: 32px;
        }

        .salary-table {
          width: 100%;
          border-collapse: collapse;
          border: 1px solid #F0F1F3;
          border-radius: 8px;
          overflow: hidden;
        }

        .salary-table th {
          background-color: #F8F9FA;
          padding: 16px;
          font-size: 12px;
          font-weight: 600;
          color: #667085;
          text-align: left;
          border-bottom: 1px solid #F0F1F3;
        }

        .salary-table th:not(:last-child) {
          border-right: 1px solid #F0F1F3;
        }

        .salary-table td {
          padding: 16px;
          border-bottom: 1px solid #F0F1F3;
          font-size: 12px;
          color: #333843;
        }

        .salary-table td:not(:last-child) {
          border-right: 1px solid #F0F1F3;
        }

        .salary-table .main-row td {
          font-weight: 500;
        }

        .salary-table .description-row td {
          font-size: 11px;
          color: #667085;
          padding-top: 4px;
          padding-bottom: 16px;
        }

        .amount-cell {
          text-align: right;
          font-weight: 500;
        }

        /* Totals Section */
        .totals-section {
          display: flex;
          justify-content: flex-end;
          margin-bottom: 32px;
        }

        .totals-container {
          width: 300px;
        }

        .totals-table {
          width: 100%;
          border-collapse: collapse;
        }

        .totals-table td {
          padding: 8px 0;
          font-size: 12px;
        }

        .totals-table .total-label {
          color: #667085;
          text-align: left;
        }

        .totals-table .total-value {
          color: #333843;
          text-align: right;
          font-weight: 500;
        }

        .totals-table .final-total td {
          border-top: 2px solid #F0F1F3;
          padding-top: 12px;
          font-weight: 600;
          font-size: 14px;
        }

        .totals-table .empty-row td {
          padding: 4px 0;
        }

        /* Footer */
        .slip-footer {
          border-top: 1px solid #F0F1F3;
          padding-top: 24px;
        }

        .footer-content {
          text-align: center;
        }

        .terms-title {
          font-size: 14px;
          font-weight: 600;
          color: #333843;
          margin-bottom: 8px;
        }

        .terms-text {
          font-size: 12px;
          color: #667085;
          line-height: 1.5;
        }
      CSS
    end

    def render_header_section
      # Try to read SVG and convert to optimized format for PDF
      logo_content = render_logo_content

      <<~HTML
        <header class="slip-header">
          <div class="logo-container">
            #{logo_content}
          </div>
          <div class="header-info">
            <h1 class="company-name">#{@config.company_name}</h1>
            <div class="company-info">
              <p>#{@employee.name}</p>
              <p>#{@config.company_address}</p>
              <p>#{@config.company_phone} | #{@config.company_email}</p>
              <p>#{Date.current.strftime('%d/%m/%Y')}</p>
            </div>
          </div>
          <div class="total-amount-header">
            <div class="total-label">Total Amount</div>
            <div class="total-value">#{format_currency(@salary_calculation.net_salary)}</div>
          </div>
        </header>
      HTML
    end

    def render_logo_content
      begin
        require 'base64'

        # Try PNG first, then fallback to SVG
        png_path = Rails.root.join('app', 'assets', 'images', 'athar_logo.png')
        svg_path = Rails.root.join('app', 'assets', 'images', 'athar_logo.svg')

        if File.exist?(png_path)
          # Use PNG with base64 encoding for better PDF compatibility
          png_data = File.read(png_path)
          base64_png = Base64.strict_encode64(png_data)
          %(<img src="data:image/png;base64,#{base64_png}" alt="Company Logo" />)
        elsif File.exist?(svg_path)
          # Fallback to SVG
          svg_content = File.read(svg_path)
          svg_content
        else
          '<div class="logo-placeholder">A</div>'
        end
      rescue => e
        Rails.logger.error "Failed to load logo: #{e.message}"
        '<div class="logo-placeholder">A</div>'
      end
    end

    def render_employee_details_section
      <<~HTML
        <section class="employee-details">
          <div class="details-left">
            <div class="detail-item">
              <div class="detail-label">Date</div>
              <div class="detail-value">#{Date.current.strftime('%d/%m/%Y')}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Salary Month</div>
              <div class="detail-value">#{format_salary_month(salary_calculation.period)}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Industry</div>
              <div class="detail-value">#{company_industry}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Payment Type</div>
              <div class="detail-value">#{default_payment_method}</div>
            </div>
          </div>

          <div class="details-right">
            <div class="employee-info">
              <div class="job-title">#{employee_job_title}</div>
              <div class="employee-name">#{@employee.name}</div>
              <div class="contact-info">
                #{employee_contact_display.map { |line| "<div>#{line}</div>" }.join("\n                ")}
              </div>

              <div class="note-section">
                <div class="note-label">Note</div>
                <div class="note-text">
                  This salary slip is generated automatically and contains confidential information. Please keep it secure.
                </div>
              </div>
            </div>
          </div>
        </section>
      HTML
    end

    def render_salary_table_section
      <<~HTML
        <section class="salary-table-section">
          <table class="salary-table">
            <thead>
              <tr>
                <th>ARTICLE</th>
                <th>AMOUNT</th>
                <th>DEDUCTIONS</th>
                <th>FINAL AMOUNT</th>
              </tr>
            </thead>
            <tbody>
              #{render_salary_rows}
            </tbody>
          </table>
        </section>
      HTML
    end

    def render_salary_rows
      rows = []

      # Base Salary
      rows << <<~HTML
        <tr class="main-row">
          <td>Basic</td>
          <td class="amount-cell">#{format_currency(salary_calculation.salary_package.base_salary)}</td>
          <td class="amount-cell">#{format_currency(0)}</td>
          <td class="amount-cell">#{format_currency(salary_calculation.salary_package.base_salary)}</td>
        </tr>
        <tr class="description-row">
          <td colspan="4">Base salary for the period</td>
        </tr>
      HTML



      # Transportation Allowance
      if salary_calculation.salary_package.transportation_allowance.to_f > 0
        rows << <<~HTML
          <tr class="main-row">
            <td>Transportation allowance</td>
            <td class="amount-cell">#{format_currency(salary_calculation.salary_package.transportation_allowance)}</td>
            <td class="amount-cell">#{format_currency(0)}</td>
            <td class="amount-cell">#{format_currency(salary_calculation.salary_package.transportation_allowance)}</td>
          </tr>
          <tr class="description-row">
            <td colspan="4">Transportation allowance for commuting expenses</td>
          </tr>
        HTML
      end

      # Other Allowances
      if salary_calculation.salary_package.other_allowances.to_f > 0
        rows << <<~HTML
          <tr class="main-row">
            <td>Other allowances</td>
            <td class="amount-cell">#{format_currency(salary_calculation.salary_package.other_allowances)}</td>
            <td class="amount-cell">#{format_currency(0)}</td>
            <td class="amount-cell">#{format_currency(salary_calculation.salary_package.other_allowances)}</td>
          </tr>
          <tr class="description-row">
            <td colspan="4">Additional allowances and benefits</td>
          </tr>
        HTML
      end

      # Deductions
      if salary_calculation.deductions.present?
        salary_calculation.deductions.each do |deduction_type, amount|
          next if amount.to_f <= 0
          rows << <<~HTML
            <tr class="main-row">
              <td>#{deduction_type.humanize}</td>
              <td class="amount-cell">#{format_currency(0)}</td>
              <td class="amount-cell">#{format_currency(amount)}</td>
              <td class="amount-cell">#{format_currency(-amount.to_f)}</td>
            </tr>
            <tr class="description-row">
              <td colspan="4">#{deduction_description(deduction_type)}</td>
            </tr>
          HTML
        end
      end

      rows.join("\n")
    end

    def render_totals_section
      <<~HTML
        <section class="totals-section">
          <div class="totals-container">
            <table class="totals-table">
              <tr>
                <td class="total-label">Total Deductions</td>
                <td class="total-value">#{format_currency(salary_calculation.total_deductions || 0)}</td>
              </tr>
              <tr>
                <td class="total-label">Total Taxes</td>
                <td class="total-value">#{format_currency(calculate_total_taxes || 0)}</td>
              </tr>
              <tr>
                <td class="total-label">Total VAT</td>
                <td class="total-value">#{format_currency(0)}</td>
              </tr>
              <tr class="empty-row">
                <td></td>
                <td></td>
              </tr>
              <tr class="final-total">
                <td class="total-label">Total Price</td>
                <td class="total-value">#{format_currency(salary_calculation.net_salary)}</td>
              </tr>
            </table>
          </div>
        </section>
      HTML
    end

    def render_footer_section
      <<~HTML
        <footer class="slip-footer">
          <div class="footer-content">
            <div class="terms-title">Terms & Conditions</div>
            <div class="terms-text">
              This is a computer-generated document and does not require a signature.
              Please keep this document for your records.
            </div>
          </div>
        </footer>
      HTML
    end

    # Helper method for deduction descriptions
    def deduction_description(deduction_type)
      case deduction_type.to_s.downcase
      when 'income_tax'
        'Income tax deduction as per government regulations'
      when 'social_security'
        'Social security contribution'
      when 'health_insurance'
        'Health insurance premium'
      when 'pension'
        'Pension fund contribution'
      else
        "#{deduction_type.humanize} deduction"
      end
    end
  end
end
