# Attendance System Transformation Plan (IMMEDIATE IMPLEMENTATION)

## Overview
Immediate implementation of simplified attendance system with only work/leave states, monthly aggregation, and Friday-Saturday weekends.

## System Requirements

### Core Business Rules
- **Two Period Types**: Only "work" and "leave"
- **Leave Detection**: < 5 hours work per day = full-day leave
- **Accumulated Hours**: Missing hours accumulate across days
- **Leave Conversion**: > 9 accumulated hours = 1 full-day leave + remainder
- **Monthly Expectation**: Fixed 180 hours per month
- **Weekend Logic**: Friday-Saturday weekends
- **Multiple Punches**: Allowed per day, no break classifications

### Target System Architecture
- **Daily Processing**: Events → Simple Daily Periods (work/leave only) → Daily Summaries
- **Monthly Aggregation**: On-demand calculation during salary processing
- **Simple Salary Integration**: Single deduction based on monthly hours deficit
- **Clean API Structure**: Simplified endpoints with only work/leave filtering

## Implementation Requirements

### 1. Data Models

#### Updated Period Model (Extends Existing)
```ruby
# app/models/attendance/period.rb
module Attendance
  class Period < ApplicationRecord
    include Athar::Commons::Models::Concerns::Ransackable

    # Use the existing table name for compatibility
    self.table_name = 'attendance_periods'

    belongs_to :employee

    validates :date, presence: true
    validates :period_type, presence: true
    validates :start_timestamp, presence: true
    validates :end_timestamp, presence: true
    validates :duration_minutes, presence: true
    validates :auto_generated, inclusion: { in: [true, false] }

    # Extended period types - keep existing + add new
    PERIOD_TYPES = {
      work: 'work',
      break: 'break',                    # Keep existing
      late: 'late',                      # Keep existing
      early_departure: 'early_departure', # Keep existing
      early_arrival: 'early_arrival',    # Keep existing
      leave: 'leave'                     # Add new type
    }.freeze

    # Existing scopes (keep for compatibility)
    scope :for_date, ->(date) { where(date: date) }
    scope :for_date_range, ->(start_date, end_date) { where(date: start_date..end_date) }
    scope :by_type, ->(type) { where(period_type: type) }
    scope :work_periods, -> { where(period_type: PERIOD_TYPES[:work]) }
    scope :break_periods, -> { where(period_type: PERIOD_TYPES[:break]) }
    scope :late_periods, -> { where(period_type: PERIOD_TYPES[:late]) }
    scope :early_departure_periods, -> { where(period_type: PERIOD_TYPES[:early_departure]) }
    scope :early_arrival_periods, -> { where(period_type: PERIOD_TYPES[:early_arrival]) }
    scope :predicted, -> { where(is_predicted: true) }

    # New scopes for simplified system
    scope :leave_periods, -> { where(period_type: 'leave') }
    scope :manual_leaves, -> { where(period_type: 'leave', auto_generated: false) }
    scope :auto_leaves, -> { where(period_type: 'leave', auto_generated: true) }
    scope :simplified_work, -> { where(period_type: ['work', 'break', 'late', 'early_departure', 'early_arrival']) }
    scope :for_month, ->(year, month) { where(date: Date.new(year, month, 1)..Date.new(year, month, -1)) }

    # Helper methods for working with timestamps (keep existing)
    def start_time
      Time.at(start_timestamp)
    end

    def end_time
      Time.at(end_timestamp)
    end

    def duration_hours
      duration_minutes / 60.0
    end

    # New helper methods
    def full_day_leave?
      period_type == 'leave' && duration_minutes >= 480 # 8 hours
    end

    def manual_leave?
      period_type == 'leave' && !auto_generated?
    end

    def auto_leave?
      period_type == 'leave' && auto_generated?
    end

    def simplified_type
      case period_type
      when 'work', 'break', 'late', 'early_departure', 'early_arrival'
        'work'
      when 'leave'
        'leave'
      else
        'work'
      end
    end

    # Keep existing class methods for compatibility
    def self.calculate_for_employee_date(employee, date, incomplete_day = false)
      # Will be updated to use new simplified service when ready
      Attendance::PeriodService.new(employee, date, incomplete_day).calculate_periods
    end

    def self.recalculate_for_date_range(employee, start_date, end_date)
      (start_date..end_date).each do |date|
        incomplete_day = (date == Date.today)
        calculate_for_employee_date(employee, date, incomplete_day)
      end
    end
  end
end
```

#### New Accumulated Hours Model
```ruby
# app/models/attendance/accumulated_hours.rb
module Attendance
  class AccumulatedHours < ApplicationRecord
    belongs_to :employee

    validates :year, :month, presence: true
    validates :year, numericality: { greater_than: 2020, less_than: 2100 }
    validates :month, numericality: { greater_than: 0, less_than: 13 }
    validates :accumulated_missing_hours, presence: true, numericality: { greater_than_or_equal_to: 0 }
    validates :employee_id, uniqueness: { scope: [:year, :month] }

    def self.for_employee_month(employee, year, month)
      find_or_create_by(employee: employee, year: year, month: month) do |record|
        record.accumulated_missing_hours = 0.0
      end
    end

    def add_missing_hours(hours)
      increment!(:accumulated_missing_hours, hours)
    end

    def convert_to_leave_days
      full_days = (accumulated_missing_hours / 8.0).floor
      remainder = accumulated_missing_hours % 8.0

      return full_days, remainder
    end

    def reset_to_remainder(remainder)
      update!(accumulated_missing_hours: remainder)
    end
  end
end
```

#### New Monthly Record Model
```ruby
# app/models/attendance/monthly_record.rb
module Attendance
  class MonthlyRecord < ApplicationRecord
    belongs_to :employee

    validates :year, :month, presence: true
    validates :year, numericality: { greater_than: 2020, less_than: 2100 }
    validates :month, numericality: { greater_than: 0, less_than: 13 }
    validates :total_work_hours, presence: true, numericality: { greater_than_or_equal_to: 0 }
    validates :expected_work_hours, presence: true, numericality: { greater_than: 0 }
    validates :leave_days, presence: true, numericality: { greater_than_or_equal_to: 0 }
    validates :work_days_present, presence: true, numericality: { greater_than_or_equal_to: 0 }
    validates :work_days_expected, presence: true, numericality: { greater_than: 0 }
    validates :employee_id, uniqueness: { scope: [:year, :month] }

    # Status enum
    enum status: { work: 0, leave: 1 }

    def attendance_rate
      return 0 if expected_work_hours == 0
      (total_work_hours / expected_work_hours * 100).round(2)
    end

    def hours_deficit
      [expected_work_hours - total_work_hours, 0].max
    end

    def hours_surplus
      [total_work_hours - expected_work_hours, 0].max
    end
  end
end
```

### 2. Database Migrations & Seeds

#### Migration 1: Add Auto-Generated Flag
```ruby
# db/migrate/xxx_add_auto_generated_to_attendance_periods.rb
class AddAutoGeneratedToAttendancePeriods < ActiveRecord::Migration[8.0]
  def change
    add_column :attendance_periods, :auto_generated, :boolean, default: false, null: false
    add_index :attendance_periods, :auto_generated
  end
end
```

#### Migration 2: Clean Historical Data (Safe Approach)
```ruby
# db/migrate/xxx_clean_attendance_data.rb
class CleanAttendanceData < ActiveRecord::Migration[8.0]
  def up
    # Safe deletion respecting foreign key constraints
    execute "DELETE FROM attendance_periods;"
    execute "DELETE FROM attendance_events;"
    execute "DELETE FROM attendance_summaries;"

    # Reset auto-increment sequences if needed
    execute "ALTER SEQUENCE attendance_periods_id_seq RESTART WITH 1;" if sequence_exists?('attendance_periods_id_seq')
    execute "ALTER SEQUENCE attendance_events_id_seq RESTART WITH 1;" if sequence_exists?('attendance_events_id_seq')
    execute "ALTER SEQUENCE attendance_summaries_id_seq RESTART WITH 1;" if sequence_exists?('attendance_summaries_id_seq')
  end

  def down
    # Cannot restore deleted data
    raise ActiveRecord::IrreversibleMigration
  end
end
```

#### Migration 3: Create Accumulated Hours Table
```ruby
# db/migrate/xxx_create_attendance_accumulated_hours.rb
class CreateAttendanceAccumulatedHours < ActiveRecord::Migration[8.0]
  def change
    create_table :attendance_accumulated_hours do |t|
      t.references :employee, null: false, foreign_key: true
      t.integer :year, null: false
      t.integer :month, null: false  # 1-12
      t.decimal :accumulated_missing_hours, precision: 8, scale: 2, default: 0.0
      t.text :conversion_log  # Track leave conversions

      t.timestamps
    end

    add_index :attendance_accumulated_hours, [:employee_id, :year, :month],
              unique: true, name: 'idx_accumulated_hours_employee_month'
    add_index :attendance_accumulated_hours, [:year, :month]
  end
end
```

#### Migration 4: Create Monthly Records Table
```ruby
# db/migrate/xxx_create_attendance_monthly_records.rb
class CreateAttendanceMonthlyRecords < ActiveRecord::Migration[8.0]
  def change
    create_table :attendance_monthly_records do |t|
      t.references :employee, null: false, foreign_key: true
      t.integer :year, null: false
      t.integer :month, null: false  # 1-12

      # Working hours tracking (fixed 180 hours expected per month)
      t.decimal :total_work_hours, precision: 8, scale: 2, default: 0.0
      t.decimal :expected_work_hours, precision: 8, scale: 2, default: 180.0

      # Days tracking
      t.integer :work_days_present, default: 0
      t.integer :work_days_expected, null: false
      t.integer :leave_days, default: 0

      # Status and metadata
      t.integer :status, default: 0  # work: 0, leave: 1
      t.datetime :last_calculated_at
      t.text :calculation_notes

      t.timestamps
    end

    add_index :attendance_monthly_records, [:employee_id, :year, :month],
              unique: true, name: 'idx_monthly_records_employee_month'
    add_index :attendance_monthly_records, [:year, :month]
    add_index :attendance_monthly_records, :status
  end
end
```

#### Migration 5: Update Settings & Weekend Configuration
```ruby
# db/migrate/xxx_update_attendance_settings.rb
class UpdateAttendanceSettings < ActiveRecord::Migration[8.0]
  def up
    # Core attendance settings - use consistent 'attendance' namespace
    settings = [
      { key: 'monthly_expected_hours', value: '180.0', description: 'Fixed monthly expected work hours' },
      { key: 'daily_work_hours_threshold', value: '5.0', description: 'Minimum hours to avoid full-day leave' },
      { key: 'accumulated_hours_threshold', value: '9.0', description: 'Accumulated missing hours threshold for leave conversion' },
      { key: 'standard_work_hours_per_day', value: '8.0', description: 'Standard work hours per day' },
      { key: 'weekend_days', value: '5,6', description: 'Weekend days (0=Sunday, 1=Monday, ..., 6=Saturday)' }
    ]

    settings.each do |setting|
      Setting.find_or_create_by(namespace: 'attendance', key: setting[:key]) do |s|
        s.value = setting[:value]
        s.description = setting[:description]
      end
    end

    # Optional: Create dedicated weekend configuration table for flexibility
    create_table :weekend_configurations do |t|
      t.string :name, null: false
      t.text :weekend_days, null: false  # JSON array: [5, 6]
      t.boolean :active, default: true
      t.timestamps
    end

    # Insert default weekend configuration
    execute <<-SQL
      INSERT INTO weekend_configurations (name, weekend_days, active, created_at, updated_at)
      VALUES ('Default', '[5, 6]', true, NOW(), NOW());
    SQL
  end

  def down
    drop_table :weekend_configurations if table_exists?(:weekend_configurations)

    Setting.where(namespace: 'attendance', key: [
      'monthly_expected_hours', 'daily_work_hours_threshold',
      'accumulated_hours_threshold', 'standard_work_hours_per_day', 'weekend_days'
    ]).delete_all
  end
end
```

#### Seeds File: Attendance Configuration
```ruby
# db/seeds/10_attendance_settings.rb

# Core attendance settings
attendance_settings = [
  {
    namespace: 'attendance',
    key: 'monthly_expected_hours',
    value: '180.0',
    description: 'Fixed monthly expected work hours for all employees'
  },
  {
    namespace: 'attendance',
    key: 'daily_work_hours_threshold',
    value: '5.0',
    description: 'Minimum daily work hours to avoid auto-generated full-day leave'
  },
  {
    namespace: 'attendance',
    key: 'accumulated_hours_threshold',
    value: '9.0',
    description: 'Accumulated missing hours threshold for automatic leave conversion'
  },
  {
    namespace: 'attendance',
    key: 'standard_work_hours_per_day',
    value: '8.0',
    description: 'Standard expected work hours per day'
  }
]

attendance_settings.each do |setting|
  Setting.find_or_create_by(
    namespace: setting[:namespace],
    key: setting[:key]
  ) do |s|
    s.value = setting[:value]
    s.description = setting[:description]
  end
end

# Weekend configuration - keep in attendance namespace for consistency
weekend_setting = {
  namespace: 'attendance',
  key: 'weekend_days',
  value: '5,6',
  description: 'Weekend days (0=Sunday, 1=Monday, 2=Tuesday, 3=Wednesday, 4=Thursday, 5=Friday, 6=Saturday)'
}

Setting.find_or_create_by(
  namespace: weekend_setting[:namespace],
  key: weekend_setting[:key]
) do |s|
  s.value = weekend_setting[:value]
  s.description = weekend_setting[:description]
end

# Weekend configuration (if using dedicated table approach)
if defined?(WeekendConfiguration) && WeekendConfiguration.table_exists?
  WeekendConfiguration.find_or_create_by(name: 'Default') do |config|
    config.weekend_days = '[5, 6]'  # Friday, Saturday
    config.active = true
  end

  # Optional: Additional weekend configurations for different regions/departments
  WeekendConfiguration.find_or_create_by(name: 'Middle East Standard') do |config|
    config.weekend_days = '[5, 6]'  # Friday, Saturday
    config.active = false
  end

  WeekendConfiguration.find_or_create_by(name: 'Western Standard') do |config|
    config.weekend_days = '[0, 6]'  # Sunday, Saturday
    config.active = false
  end
end

puts "✅ Attendance settings and weekend configurations seeded successfully"
```

### 3. Auto-Generated Leave Approval Models (Optional)

#### Option 2: AutoGeneratedLeave Model (If Approval Required)
```ruby
# app/models/auto_generated_leave.rb
class AutoGeneratedLeave < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable
  include Athar::Commons::Models::Concerns::ActsAsApprovable

  belongs_to :employee
  belongs_to :attendance_period, class_name: 'Attendance::Period'

  enum :status, {
    pending: 0,
    approved: 1,
    rejected: 2,
    system_approved: 3
  }, default: :pending

  validates :date, presence: true
  validates :reason, presence: true

  # Approval workflow integration
  def approval_action
    "approve_auto_generated_leave"
  end

  def system_name
    "people"
  end

  def on_approval_status_change(new_status, previous_status)
    Rails.logger.info("Auto-generated leave callback: status changed from #{previous_status} to #{new_status}")

    case new_status
    when "approved"
      update!(status: :approved)
      attendance_period.update!(status: 'approved', notes: "#{attendance_period.notes} - Approved by manager")
    when "rejected"
      update!(status: :rejected)
      # Convert leave period back to work or handle rejection
      handle_rejection
    end
  end

  def approval_context
    {
      leave_type: 'auto_generated',
      reason: reason,
      priority: 'low',
      employee_name: employee.name,
      date: date.to_s
    }
  end

  private

  def handle_rejection
    # Business decision: What happens when auto-generated leave is rejected?
    # Option 1: Convert back to work period (if possible)
    # Option 2: Mark as unpaid leave
    # Option 3: Flag for HR review

    attendance_period.update!(
      notes: "#{attendance_period.notes} - Rejected by manager, requires HR review",
      status: 'rejected_pending_hr_review'
    )
  end
end
```

#### Option 3: LeaveReviewTask Model (Retrospective Review)
```ruby
# app/models/leave_review_task.rb
class LeaveReviewTask < ApplicationRecord
  belongs_to :employee
  belongs_to :attendance_period, class_name: 'Attendance::Period'
  belongs_to :assigned_to, class_name: 'Employee'

  enum :status, {
    pending_review: 0,
    approved: 1,
    rejected: 2,
    escalated: 3
  }, default: :pending_review

  validates :due_date, presence: true
  validates :notes, presence: true

  scope :overdue, -> { where('due_date < ?', Date.current) }
  scope :due_soon, -> { where(due_date: Date.current..Date.current + 2.days) }

  def approve!(reviewer_id, notes = nil)
    update!(
      status: :approved,
      reviewed_by_id: reviewer_id,
      reviewed_at: Time.current,
      review_notes: notes
    )

    attendance_period.update!(
      notes: "#{attendance_period.notes} - Reviewed and approved by manager"
    )
  end

  def reject!(reviewer_id, notes)
    update!(
      status: :rejected,
      reviewed_by_id: reviewer_id,
      reviewed_at: Time.current,
      review_notes: notes
    )

    # Handle rejection - convert to unpaid or flag for HR
    attendance_period.update!(
      notes: "#{attendance_period.notes} - Rejected by manager: #{notes}"
    )
  end
end
```

### 4. Business Logic Services

#### New Leave Detection Service
```ruby
# app/services/attendance/period_service.rb
module Attendance
  class PeriodService
    def initialize(employee, date)
      @employee = employee
      @date = date
      @settings = load_settings
    end

    def calculate_periods
      # Clear any existing periods for the day
      clear_existing_periods

      # Get all events for the day
      events = get_valid_events

      # Check if employee is on approved leave
      if employee_on_approved_leave?
        create_leave_period('Approved leave')
        return
      end

      # Calculate total work hours from all punches
      total_work_hours = calculate_total_work_hours(events)

      # Apply new leave detection logic
      if total_work_hours < @settings[:daily_threshold]  # Less than 5 hours = full-day leave
        create_leave_period("Insufficient work hours: #{total_work_hours.round(2)}h")
        track_missing_hours(@settings[:standard_hours] - total_work_hours)
      else
        create_work_period(total_work_hours)
        if total_work_hours < @settings[:standard_hours]
          track_missing_hours(@settings[:standard_hours] - total_work_hours)
        end
      end

      # Check accumulated missing hours for leave conversion
      check_accumulated_hours_for_leave_conversion
    end

    private

    def load_settings
      {
        daily_threshold: Setting.get('attendance', 'daily_work_hours_threshold', '5.0').to_f,
        standard_hours: Setting.get('attendance', 'standard_work_hours_per_day', '8.0').to_f,
        accumulated_threshold: Setting.get('attendance', 'accumulated_hours_threshold', '9.0').to_f,
        weekend_days: Setting.get('attendance', 'weekend_days', '5,6').split(',').map(&:to_i)
      }
    end

    def clear_existing_periods
      @employee.attendance_periods.where(date: @date).destroy_all
    end

    def get_valid_events
      @employee.attendance_events
               .where(date: @date)
               .where.not(duplicate: true)
               .order(timestamp: :asc)
    end

    def employee_on_approved_leave?
      @employee.leaves
               .approved
               .where("start_date <= ? AND end_date >= ?", @date, @date)
               .exists?
    end

    def calculate_total_work_hours(events)
      # Simple logic: pair check-ins with check-outs, sum all work periods
      # Multiple punches allowed, no break classifications
      total_minutes = 0

      events.each_cons(2) do |event1, event2|
        if event1.check_in? && event2.check_out?
          duration = (event2.timestamp - event1.timestamp) / 60
          total_minutes += duration if duration > 0
        end
      end

      total_minutes / 60.0  # Convert to hours
    end

    def create_leave_period(reason)
      # Determine if this is auto-generated or manual leave
      auto_generated = !employee_on_approved_leave?

      Attendance::Period.create!(
        employee: @employee,
        date: @date,
        period_type: 'leave',
        start_timestamp: @date.beginning_of_day.to_i,
        end_timestamp: @date.end_of_day.to_i,
        duration_minutes: (@settings[:standard_hours] * 60).round,
        auto_generated: auto_generated,
        notes: "#{auto_generated ? 'Auto-generated' : 'Manual'} leave: #{reason}"
      )
    end

    def create_work_period(total_hours)
      return if total_hours <= 0

      Attendance::Period.create!(
        employee: @employee,
        date: @date,
        period_type: 'work',
        start_timestamp: @date.beginning_of_day.to_i,
        end_timestamp: @date.end_of_day.to_i,
        duration_minutes: (total_hours * 60).round,
        notes: "Work period: #{total_hours.round(2)} hours"
      )
    end

    def track_missing_hours(missing_hours)
      return unless missing_hours > 0

      accumulated_record = Attendance::AccumulatedHours.for_employee_month(@employee, @date.year, @date.month)
      accumulated_record.add_missing_hours(missing_hours)
    end

    def check_accumulated_hours_for_leave_conversion
      accumulated_record = Attendance::AccumulatedHours.for_employee_month(@employee, @date.year, @date.month)

      if accumulated_record.accumulated_missing_hours >= @settings[:accumulated_threshold]
        convert_accumulated_to_leave(accumulated_record)
      end
    end

    def convert_accumulated_to_leave(accumulated_record)
      full_days, remainder = accumulated_record.convert_to_leave_days

      # Find work periods in the same month that can be converted to leave
      work_periods_to_convert = find_work_days_to_convert(accumulated_record, full_days)

      work_periods_to_convert.each do |work_period|
        work_period.update!(
          period_type: 'leave',
          auto_generated: true,  # Mark as auto-generated leave
          notes: 'Auto-generated leave: Converted from accumulated missing hours'
        )
      end

      # Update accumulated hours to remainder
      accumulated_record.update!(
        accumulated_missing_hours: remainder,
        conversion_log: "Converted #{full_days} days, remainder: #{remainder.round(2)} hours at #{Time.current}"
      )
    end

    def find_work_days_to_convert(accumulated_record, days_needed)
      # Find work periods in the same month that can be converted to leave
      start_date = Date.new(accumulated_record.year, accumulated_record.month, 1)
      end_date = start_date.end_of_month

      @employee.attendance_periods
               .where(date: start_date..end_date, period_type: 'work')
               .order(date: :desc)  # Convert most recent work days first
               .limit(days_needed)
    end
  end
end
```

#### Monthly Calculation Service (Called During Salary Processing)
```ruby
# New service: Attendance::MonthlyCalculationService
class Attendance::MonthlyCalculationService
  def initialize(employee, year, month)
    @employee = employee
    @year = year
    @month = month
    @start_date = Date.new(year, month, 1)
    @end_date = @start_date.end_of_month
  end

  def calculate
    # 1. Fixed expected hours per month
    expected_work_hours = 180.0  # Fixed monthly expectation

    # 2. Calculate expected working days
    expected_work_days = calculate_expected_work_days

    # 3. Get leave days (approved leaves + auto-generated leaves)
    leave_days = calculate_total_leave_days

    # 4. Calculate actual work hours from daily periods
    actual_work_hours = calculate_actual_work_hours

    # 5. Get accumulated missing hours
    accumulated_missing_hours = calculate_accumulated_missing_hours

    # 6. Calculate work days present
    work_days_present = calculate_work_days_present

    # 7. Determine overall status
    status = determine_monthly_status(leave_days, expected_work_days)

    # 8. Create or update monthly record
    create_or_update_monthly_record(
      expected_work_hours: expected_work_hours,
      actual_work_hours: actual_work_hours,
      work_days_expected: expected_work_days,
      work_days_present: work_days_present,
      leave_days: leave_days,
      accumulated_missing_hours: accumulated_missing_hours,
      status: status
    )
  end

  private

  def calculate_total_leave_days
    # Approved leaves + auto-generated leaves from attendance
    approved_leaves = @employee.leaves
                              .approved
                              .where("start_date <= ? AND end_date >= ?", @end_date, @start_date)
                              .sum { |leave| calculate_leave_days_in_month(leave) }

    auto_leaves = @employee.attendance_periods
                           .where(date: @start_date..@end_date)
                           .where(period_type: 'leave')
                           .count

    approved_leaves + auto_leaves
  end

  def calculate_actual_work_hours
    # Sum all work periods for the month
    @employee.attendance_periods
             .where(date: @start_date..@end_date)
             .where(period_type: 'work')
             .sum(:duration_minutes) / 60.0
  end
end
```

### 3. Event Processing Simplification

#### Simplified Period Types
- **Remove**: break, late, early_departure, early_arrival
- **Keep**: work, leave (only two types)
- **Logic**: Multiple punches per day allowed, no break classifications

### 4. API and Controller Changes

#### Updated Daily Period Logic
```ruby
# Updated: Daily period calculation with simplified logic
def calculate_daily_attendance
  # Expectation: Two records per day (check-in, check-out)
  # Reality: Multiple punches allowed, no classifications

  events = employee.attendance_events.for_date(date)
  total_work_hours = calculate_work_hours_from_punches(events)

  if total_work_hours < 5.0
    # Less than 5 hours = full-day leave
    create_leave_period(date)
    accumulate_missing_hours(8.0 - total_work_hours)
  else
    # 5+ hours = work day
    create_work_period(date, total_work_hours)
    if total_work_hours < 8.0
      accumulate_missing_hours(8.0 - total_work_hours)
    end
  end
end
```

#### Monthly Attendance Endpoint
```ruby
# New endpoint: GET /api/attendance/monthly/:employee_id
def monthly_summary
  year = params[:year]&.to_i || Date.current.year
  month = params[:month]&.to_i || Date.current.month

  # Calculate on-demand during request (no pre-calculation)
  service = Attendance::MonthlyCalculationService.new(@employee, year, month)
  monthly_record = service.calculate

  render json: {
    data: {
      employee_id: @employee.id,
      year: year,
      month: month,
      total_work_hours: monthly_record.total_work_hours,
      expected_work_hours: 180.0,  # Fixed monthly expectation
      work_days_present: monthly_record.work_days_present,
      work_days_expected: monthly_record.work_days_expected,
      leave_days: monthly_record.leave_days,
      accumulated_missing_hours: monthly_record.accumulated_missing_hours,
      status: monthly_record.status,
      attendance_rate: (monthly_record.total_work_hours / 180.0 * 100).round(2),
      last_calculated_at: monthly_record.last_calculated_at
    }
  }
end
```

### 5. Background Job Changes

#### Simplified Daily Processing
```ruby
# Updated: Attendance::PeriodCalculationWorker (Simplified)
class Attendance::PeriodCalculationWorker
  include Sidekiq::Worker

  def perform(employee_id, date_string)
    employee = Employee.find(employee_id)
    date = Date.parse(date_string)

    # Simplified period calculation
    Attendance::PeriodService.new(employee, date).calculate_periods

    # Check for accumulated hours conversion
    check_accumulated_hours_conversion(employee)
  end

  private

  def check_accumulated_hours_conversion(employee)
    # Check if accumulated missing hours > 9, convert to leave
    if employee.accumulated_missing_hours > 9.0
      full_days = (employee.accumulated_missing_hours / 8.0).floor
      remainder = employee.accumulated_missing_hours % 8.0

      employee.convert_accumulated_to_leave(full_days, remainder)
    end
  end
end
```

#### No Pre-scheduled Monthly Calculations
```yaml
# config/sidekiq_scheduler.yml
# Remove monthly calculation jobs - calculations happen during salary processing

# Keep daily period calculation
attendance_period_daily_calculation:
  cron: "0 1 * * *"  # Run at 1am every day
  class: Attendance::BatchPeriodCalculationWorker
  queue: attendance
  description: "Calculate attendance periods for all employees for yesterday"
  enabled: true
```

### 4. Monthly Calculation Service

```ruby
# app/services/attendance/monthly_calculation_service.rb
module Attendance
  class MonthlyCalculationService
    def initialize(employee, year, month)
      @employee = employee
      @year = year
      @month = month
      @start_date = Date.new(year, month, 1)
      @end_date = @start_date.end_of_month
    end

    def calculate
      # Find or create monthly record
      monthly_record = Attendance::MonthlyRecord.find_or_initialize_by(
        employee: @employee,
        year: @year,
        month: @month
      )

      # Calculate all values
      monthly_record.assign_attributes(
        total_work_hours: calculate_total_work_hours,
        expected_work_hours: 180.0,  # Fixed monthly expectation
        work_days_present: calculate_work_days_present,
        work_days_expected: calculate_expected_work_days,
        leave_days: calculate_leave_days,
        status: determine_monthly_status,
        last_calculated_at: Time.current,
        calculation_notes: generate_calculation_notes
      )

      monthly_record.save!
      monthly_record
    end

    private

    def calculate_total_work_hours
      @employee.attendance_periods
               .for_month(@year, @month)
               .work_periods
               .sum(:duration_minutes) / 60.0
    end

    def calculate_leave_days
      @employee.attendance_periods
               .for_month(@year, @month)
               .leave_periods
               .count
    end

    def calculate_work_days_present
      @employee.attendance_periods
               .for_month(@year, @month)
               .work_periods
               .count
    end

    def calculate_expected_work_days
      # Calculate working days in month (excluding weekends)
      weekend_days = get_weekend_days

      (@start_date..@end_date).count { |date| !weekend_days.include?(date.wday) }
    end

    def get_weekend_days
      # Try weekend configuration table first, fallback to settings
      if defined?(WeekendConfiguration) && WeekendConfiguration.table_exists?
        config = WeekendConfiguration.where(active: true).first
        return JSON.parse(config.weekend_days) if config
      end

      # Fallback to settings - use consistent 'attendance' namespace
      Setting.get('attendance', 'weekend_days', '5,6').split(',').map(&:to_i)
    end

    def determine_monthly_status
      leave_days = calculate_leave_days
      expected_days = calculate_expected_work_days

      # If more than 50% of expected work days are leave, status is "leave"
      leave_days > (expected_days * 0.5) ? :leave : :work
    end

    def generate_calculation_notes
      work_hours = calculate_total_work_hours
      leave_days = calculate_leave_days
      expected_days = calculate_expected_work_days

      "Work: #{work_hours.round(2)}h/180h, Leave: #{leave_days}/#{expected_days} days"
    end
  end
end
```

### 5. Salary Integration

```ruby
# app/models/concerns/salary/attendance_integration.rb
module Salary
  module AttendanceIntegration
    extend ActiveSupport::Concern

    def calculate_attendance_deductions(calculation)
      year = calculation.period_start_date.year
      month = calculation.period_start_date.month

      # Calculate monthly record on-demand during salary processing
      service = Attendance::MonthlyCalculationService.new(employee, year, month)
      monthly_record = service.calculate

      # Calculate deduction based on missing hours from fixed 180 hours
      hours_deficit = monthly_record.hours_deficit
      return 0 if hours_deficit <= 0

      # Calculate hourly rate based on fixed 180 hours
      hourly_rate = calculation.gross_salary / 180.0
      deduction = hourly_rate * hours_deficit

      # Track deduction details
      calculation.calculation_details.build(
        detail_type: 'deduction',
        category: 'attendance_hours_deficit',
        amount: deduction,
        description: "Deduction for #{hours_deficit.round(2)} missing work hours (#{monthly_record.total_work_hours.round(2)}/180.0 hours)"
      )

      deduction
    end
  end
end
```

### 7. Settings Updates

#### Updated Attendance Settings
```ruby
# db/seeds/10_settings.rb updates
Setting.set('attendance', 'monthly_expected_hours', '180.0', 'Fixed monthly expected work hours')
Setting.set('attendance', 'daily_work_hours_threshold', '5.0', 'Minimum hours to avoid full-day leave')
Setting.set('attendance', 'accumulated_hours_threshold', '9.0', 'Accumulated missing hours threshold for leave conversion')
Setting.set('attendance', 'standard_work_hours_per_day', '8.0', 'Standard work hours per day')
Setting.set('attendance', 'weekend_days', '5,6', 'Weekend days (5=Friday, 6=Saturday)')
Setting.set('attendance', 'leave_detection_enabled', 'true', 'Enable automatic leave detection from attendance')
```

### 6. API Controllers

```ruby
# app/controllers/api/attendance/periods_controller.rb
module Api
  module Attendance
    class PeriodsController < ApplicationController
      before_action :authenticate_user!
      before_action :set_employee

      def index
        collection = @employee.attendance_periods
        collection = apply_filters(collection)
        collection = apply_pagination(collection)

        render json: {
          data: serialize_periods(collection),
          meta: pagination_meta(collection)
        }
      end

      def monthly_summary
        year = params[:year]&.to_i || Date.current.year
        month = params[:month]&.to_i || Date.current.month

        service = Attendance::MonthlyCalculationService.new(@employee, year, month)
        monthly_record = service.calculate

        render json: {
          data: {
            employee_id: @employee.id,
            year: year,
            month: month,
            total_work_hours: monthly_record.total_work_hours,
            expected_work_hours: monthly_record.expected_work_hours,
            work_days_present: monthly_record.work_days_present,
            work_days_expected: monthly_record.work_days_expected,
            leave_days: monthly_record.leave_days,
            status: monthly_record.status,
            attendance_rate: monthly_record.attendance_rate,
            hours_deficit: monthly_record.hours_deficit,
            last_calculated_at: monthly_record.last_calculated_at
          }
        }
      end

      private

      def set_employee
        @employee = Employee.find(params[:employee_id])
      end

      def apply_filters(collection)
        collection = apply_date_filter(collection)
        collection = apply_period_type_filter(collection)
        collection = apply_auto_generated_filter(collection)  # ⚠️ NEW FILTER
        collection = apply_leave_type_filter(collection)      # ⚠️ NEW FILTER
        collection
      end

      def apply_date_filter(collection)
        return collection unless params[:start_date] && params[:end_date]

        start_date = Date.parse(params[:start_date])
        end_date = Date.parse(params[:end_date])
        collection.where(date: start_date..end_date)
      end

      def apply_period_type_filter(collection)
        return collection unless params[:period_type].present?

        case params[:period_type]
        when 'work'
          collection.work_periods
        when 'leave'
          collection.leave_periods
        else
          collection
        end
      end

      def apply_auto_generated_filter(collection)
        return collection unless params[:auto_generated].present?
        collection.where(auto_generated: params[:auto_generated])
      end

      def apply_leave_type_filter(collection)
        return collection unless params[:leave_type].present?

        case params[:leave_type]
        when 'manual'
          collection.where(period_type: 'leave', auto_generated: false)
        when 'auto_generated'
          collection.where(period_type: 'leave', auto_generated: true)
        else
          collection
        end
      end

      def serialize_periods(periods)
        periods.map do |period|
          {
            id: period.id,
            date: period.date,
            period_type: period.period_type,
            duration_minutes: period.duration_minutes,
            duration_hours: period.duration_hours,
            auto_generated: period.auto_generated,  # ⚠️ NEW REQUIRED FIELD
            leave_type: period.period_type == 'leave' ? (period.auto_generated? ? 'auto_generated' : 'manual') : nil,
            notes: period.notes,
            created_at: period.created_at
          }
        end
      end
    end
  end
end
```

## Implementation Checklist

### Database Setup
- [ ] **Run Migration 1**: Add auto_generated column to attendance_periods
- [ ] **Run Migration 2**: Clean historical data (safe deletion)
- [ ] **Run Migration 3**: Create accumulated hours table
- [ ] **Run Migration 4**: Create monthly records table
- [ ] **Run Migration 5**: Update settings & weekend configuration
- [ ] **Run Seeds**: Execute attendance settings and weekend configuration seeds
- [ ] **Verify Setup**: Ensure all new columns exist and settings are configured

### Models Implementation
- [ ] **Update Period Model**: Extend existing model with auto_generated flag and 'leave' type
- [ ] **Create AccumulatedHours Model**: Implement with tracking methods
- [ ] **Create MonthlyRecord Model**: Implement with calculation methods
- [ ] **Create WeekendConfiguration Model**: Optional dedicated weekend config
- [ ] **Update Employee Associations**: Add has_many for new models
- [ ] **Maintain Compatibility**: Keep all existing scopes and methods

### Services Implementation
- [ ] **Implement PeriodService**: Complete rewrite with simplified logic
- [ ] **Implement MonthlyCalculationService**: On-demand monthly aggregation
- [ ] **Update Background Workers**: Modify to use new PeriodService
- [ ] **Test Service Logic**: Verify leave detection and accumulation works

### API Implementation
- [ ] **Update PeriodsController**: Implement simplified filtering
- [ ] **Add Monthly Summary Endpoint**: Implement monthly_summary action
- [ ] **Update Serializers**: Add auto_generated field and leave type distinction
- [ ] **Test API Endpoints**: Verify all endpoints work with new logic

### Salary Integration
- [ ] **Update AttendanceIntegration**: Implement monthly hours-based deductions
- [ ] **Remove Legacy Deduction Methods**: Delete complex period-based calculations
- [ ] **Test Salary Calculations**: Verify deductions work correctly
- [ ] **Update Salary Tests**: Modify tests for new logic

### Testing & Validation
- [ ] **Unit Tests**: Update all attendance-related unit tests
- [ ] **Integration Tests**: Update tests for new simplified system
- [ ] **API Tests**: Test all endpoints with new period types and auto_generated field
- [ ] **Salary Tests**: Test monthly deduction calculations
- [ ] **End-to-End Tests**: Test complete flow from events to salary

### Settings & Configuration
- [ ] **Verify Seeded Settings**: Ensure all attendance settings are properly seeded
- [ ] **Configure Weekend Days**: Verify Friday-Saturday weekends (5,6) are set
- [ ] **Test Settings Access**: Verify Setting.get() calls work for all attendance settings
- [ ] **Test Thresholds**: Verify 5-hour and 9-hour thresholds work
- [ ] **Test Weekend Logic**: Verify Friday-Saturday weekends work correctly
- [ ] **Test Auto vs Manual Leaves**: Verify auto_generated flag works correctly
- [ ] **Test 180-Hour Logic**: Verify fixed monthly expectation works

### Documentation & Cleanup
- [ ] **Update API Documentation**: Document new endpoints and BREAKING CHANGES
- [ ] **Update User Documentation**: Document new attendance logic
- [ ] **Remove Legacy Code**: Delete unused complex period logic
- [ ] **Code Review**: Review all changes for quality and consistency

### Deployment Verification
- [ ] **Test in Staging**: Full system test in staging environment
- [ ] **Performance Testing**: Verify system performance is acceptable
- [ ] **Data Validation**: Verify historical data correctly transformed
- [ ] **User Acceptance**: Get approval from users on new system
- [ ] **Production Deployment**: Deploy to production with monitoring

## Risk Assessment

### High Risk
- **API Breaking Changes**: New auto_generated field in all responses breaks existing API consumers
- **Leave Detection Logic**: Ensuring < 5 hours work correctly triggers leave
- **Accumulated Hours Logic**: Complex cross-day accumulation and conversion
- **Salary Integration**: On-demand calculations during salary processing
- **Data Consistency**: Keeping daily and monthly data synchronized

### Medium Risk
- **Performance**: On-demand monthly calculations during salary processing
- **Multiple Punches**: Handling various punch patterns correctly
- **Leave Integration**: Coordinating approved leaves with auto-detected leaves

### Low Risk
- **Weekend Logic**: Friday-Saturday logic already established
- **Settings Updates**: Configuration changes
- **Database Schema**: Standard table creation

## Success Criteria

1. **Functional**:
   - < 5 hours work correctly creates full-day leave
   - Accumulated missing hours > 9 correctly converts to leave + remainder
   - Fixed 180 hours monthly calculation works accurately

2. **Performance**:
   - On-demand monthly calculations complete within acceptable time during salary processing
   - Daily period calculations remain fast

3. **Integration**:
   - Salary system correctly uses on-demand monthly calculations
   - Leave system properly integrates with auto-detected leaves

4. **Compatibility**:
   - Existing daily APIs continue to work with simplified logic
   - Multiple punches per day handled correctly

5. **Data Integrity**:
   - Daily periods accurately reflect simplified work/leave logic
   - Monthly aggregations correctly sum daily data

## Implementation Timeline

### Immediate Implementation (2-3 weeks)

**Week 1: Database & Models**
- Day 1-2: Run database migrations and seeds
- Day 3-4: Implement new models (Period, AccumulatedHours, MonthlyRecord)
- Day 5: Test model functionality, validations, and seeded settings

**Week 2: Services & Business Logic**
- Day 1-3: Implement PeriodService with simplified logic
- Day 4-5: Implement MonthlyCalculationService
- Day 6-7: Test leave detection and accumulation logic

**Week 3: API & Integration**
- Day 1-2: Update API controllers and endpoints
- Day 3-4: Update salary integration
- Day 5-7: Comprehensive testing and bug fixes

### Key Implementation Notes

**Business Rules Implementation:**
- ✅ **< 5 hours work = full-day leave**: Implemented in PeriodService
- ✅ **Accumulate missing hours**: Tracked in AccumulatedHours model
- ✅ **> 9 hours = leave conversion + remainder**: Implemented with Option C logic
- ✅ **Fixed 180 hours monthly**: Hardcoded in MonthlyCalculationService
- ✅ **Friday-Saturday weekends**: Configurable via settings
- ✅ **Multiple punches allowed**: Handled in calculate_total_work_hours method

**Data Flow:**
```
Events → PeriodService → Simple Periods (work/leave) → AccumulatedHours tracking → MonthlyCalculationService → Salary deductions
```

**Critical Success Factors:**
- Clean slate implementation with no historical data
- Leave detection logic works correctly (< 5 hours)
- Auto vs manual leave distinction works correctly
- Accumulated hours conversion works correctly (> 9 hours)
- Weekend configuration properly implemented
- Monthly calculations produce accurate results
- Salary deductions based on 180-hour expectation work correctly

## System Architecture Summary

### Core Components

**Models:**
- `Attendance::Period` - Only 'work' and 'leave' types
- `Attendance::AccumulatedHours` - Track missing hours across days
- `Attendance::MonthlyRecord` - Cache monthly aggregations
- `Attendance::Event` - Unchanged (existing punch data)

**Services:**
- `Attendance::PeriodService` - Simplified daily calculation
- `Attendance::MonthlyCalculationService` - On-demand monthly aggregation

**Business Rules:**
- < 5 hours work per day = full-day leave
- Missing hours accumulate across days
- > 9 accumulated hours = 1 full-day leave + remainder
- Fixed 180 hours monthly expectation
- Friday-Saturday weekends
- Multiple punches per day allowed

### Data Transformation

**Period Type Mapping:**
```sql
-- All complex types become 'work'
UPDATE attendance_periods
SET period_type = 'work'
WHERE period_type IN ('break', 'late', 'early_departure', 'early_arrival');
```

**New Tables:**
- `attendance_accumulated_hours` - Track missing hours
- `attendance_monthly_records` - Cache monthly calculations

### Integration Points

**Salary System:**
- Single deduction method based on monthly hours deficit
- Hourly rate = gross_salary / 180 hours
- Deduction = hourly_rate × hours_deficit

**API Endpoints:**
- Extended period filtering (work/break/late/early_departure/early_arrival/leave)
- New auto_generated and leave_type filtering options
- New monthly summary endpoint
- ⚠️ BREAKING CHANGE: auto_generated field added to all period responses

### Benefits

1. **Simplified Logic**: Only two period types to manage
2. **Better Performance**: Faster calculations with less complexity
3. **Easier Maintenance**: Clean codebase without legacy complexity
4. **Accurate Tracking**: Focus on actual work hours vs. expected
5. **Flexible Punching**: Multiple punches per day without classifications

## Key Updates Based on Requirements

### 1. Historical Data - Clean Slate Approach
- **Drop All Historical Data**: TRUNCATE all attendance tables for fresh start
- **No Data Migration**: No need to convert existing complex periods
- **Clean Implementation**: Start with simplified system from day one

### 2. Auto-Generated vs Manual Leaves: Comprehensive Guide

#### What is the auto_generated Field?
The `auto_generated` boolean field distinguishes between two fundamentally different types of leave periods:

#### Manual Leaves (auto_generated = false)
**Source**: Human-initiated leave requests that go through approval workflow
**Examples**:
- Vacation requests submitted by employee
- Sick leave requests with medical certificates
- Personal time off requests
- Maternity/paternity leave
- Emergency leave requests

**Characteristics**:
- Require approval workflow
- Have associated leave request records
- Planned in advance (usually)
- Employee is aware they're on leave
- Count against leave balance/entitlements
- May require documentation (medical certificates, etc.)

**Database Flow**:
```ruby
# 1. Employee submits leave request
leave_request = LeaveRequest.create!(
  employee: employee,
  start_date: '2024-01-15',
  end_date: '2024-01-15',
  leave_type: 'vacation',
  status: 'pending'
)

# 2. Manager approves leave request
leave_request.update!(status: 'approved', approved_by: manager)

# 3. System creates manual leave period
Attendance::Period.create!(
  employee: employee,
  date: '2024-01-15',
  period_type: 'leave',
  auto_generated: false,  # Manual leave
  notes: 'Manual leave: Approved vacation'
)
```

#### Auto-Generated Leaves (auto_generated = true)
**Source**: System-detected attendance patterns that indicate absence
**Examples**:
- Employee worked only 3 hours (< 5 hour threshold)
- Employee didn't punch in/out at all
- Accumulated missing hours converted to full-day leave
- System-detected patterns indicating absence

**Characteristics**:
- **NO approval workflow needed** - automatically approved by system
- NO associated leave request records
- Detected after-the-fact from attendance data
- Employee may not realize they're marked as "on leave"
- May or may not count against leave balance (business decision)
- Generated automatically by attendance calculation logic
- **System-approved status** - bypasses manual approval process

**Database Flow**:
```ruby
# 1. Employee punches in/out but works < 5 hours
events = [
  { event_type: 'check_in', timestamp: '09:00' },
  { event_type: 'check_out', timestamp: '13:30' }  # Only 4.5 hours
]

# 2. System calculates total work hours
total_hours = calculate_work_hours(events)  # = 4.5 hours

# 3. System automatically creates auto-generated leave
if total_hours < 5.0
  Attendance::Period.create!(
    employee: employee,
    date: Date.current,
    period_type: 'leave',
    auto_generated: true,   # Auto-generated leave
    notes: 'Auto-generated leave: Insufficient work hours (4.5h < 5h threshold)'
  )
end
```

#### Accumulated Hours Conversion (auto_generated = true)
**Special Case**: When missing hours accumulate across days and convert to leave

**Example Scenario**:
```ruby
# Day 1: Employee works 7 hours (missing 1 hour)
# Day 2: Employee works 6 hours (missing 2 hours)
# Day 3: Employee works 5.5 hours (missing 2.5 hours)
# Day 4: Employee works 6 hours (missing 2 hours)
# Day 5: Employee works 6.5 hours (missing 1.5 hours)
# Total missing: 9 hours

# System converts 1 full day (8 hours) to leave, keeps 1 hour remainder
work_period_to_convert = find_recent_work_period()
work_period_to_convert.update!(
  period_type: 'leave',
  auto_generated: true,  # Auto-generated from accumulation
  notes: 'Auto-generated leave: Converted from accumulated missing hours'
)
```

#### Business Logic Differences

| Aspect | Manual Leaves | Auto-Generated Leaves |
|--------|---------------|----------------------|
| **Approval Required** | ✅ Yes | ❌ No - System Auto-Approved |
| **Leave Request Record** | ✅ Yes | ❌ No |
| **Employee Awareness** | ✅ Employee requested it | ❌ May not know |
| **Advance Planning** | ✅ Usually planned | ❌ Detected after-the-fact |
| **Documentation** | ✅ May require docs | ❌ System-generated |
| **Leave Balance Impact** | ✅ Always deducted | ⚠️ Business decision |
| **Reporting** | Standard leave reports | Attendance compliance reports |
| **Payroll Impact** | Standard leave deduction | Attendance-based deduction |

#### API Response Examples

**Manual Leave Response**:
```json
{
  "id": "123",
  "attributes": {
    "date": "2024-01-15",
    "period_type": "leave",
    "auto_generated": false,
    "leave_type": "manual",
    "duration_minutes": 480,
    "notes": "Manual leave: Approved vacation",
    "leave_request_id": 456  // Associated leave request
  }
}
```

**Auto-Generated Leave Response**:
```json
{
  "id": "124",
  "attributes": {
    "date": "2024-01-16",
    "period_type": "leave",
    "auto_generated": true,
    "leave_type": "auto_generated",
    "duration_minutes": 480,
    "notes": "Auto-generated leave: Insufficient work hours (3.2h < 5h threshold)",
    "leave_request_id": null  // No associated leave request
  }
}
```

#### HR and Management Implications

**For HR Teams**:
- **Manual Leaves**: Standard leave management, track against entitlements, approval workflow
- **Auto Leaves**: Attendance compliance monitoring, no approval needed, may trigger coaching

**For Managers**:
- **Manual Leaves**: Review and approve absence requests, plan coverage
- **Auto Leaves**: Receive notifications for awareness, investigate attendance patterns

**For Payroll**:
- **Manual Leaves**: Use leave balance, standard leave pay rules
- **Auto Leaves**: Attendance deductions, may be unpaid

**For Employees**:
- **Manual Leaves**: Expected absence, planned time off
- **Auto Leaves**: May indicate attendance issues, need clarification

#### Auto-Generated Leave Implementation (No Approval Required)

**Design Decision: Auto-Generated Leaves Bypass Approval Process**

Auto-generated leaves are **automatically approved by the system** and do not require manual approval workflow.

```ruby
# Auto-generated leaves are immediately system-approved
def create_leave_period(reason)
  auto_generated = !employee_on_approved_leave?

  Attendance::Period.create!(
    employee: @employee,
    date: @date,
    period_type: 'leave',
    auto_generated: auto_generated,
    notes: "#{auto_generated ? 'Auto-generated' : 'Manual'} leave: #{reason}"
  )

  # No approval workflow for auto-generated leaves
  # They are considered "system-approved" by virtue of being auto_generated = true
end
```

**Key Implementation Points:**

1. **No ApprovalRequest Created**: Auto-generated leaves don't create approval requests
2. **No Workflow Integration**: Bypass the existing `acts_as_approvable` concern
3. **System Authority**: The attendance system has authority to create approved leaves
4. **Immediate Effect**: Auto-generated leaves take effect immediately
5. **Audit Trail**: The `auto_generated` flag provides clear audit trail

**Comparison with Manual Leaves:**

```ruby
# Manual Leave (requires approval)
manual_leave = Leave.create!(
  employee: employee,
  start_date: Date.current,
  end_date: Date.current,
  leave_type: 'vacation',
  status: 'pending'  # Will trigger approval workflow
)
# → Creates ApprovalRequest → Manager approval required → Period created when approved

# Auto-Generated Leave (no approval)
auto_period = Attendance::Period.create!(
  employee: employee,
  date: Date.current,
  period_type: 'leave',
  auto_generated: true,  # System-approved
  notes: 'Auto-generated leave: Insufficient work hours (3.2h < 5h threshold)'
)
# → No ApprovalRequest → Immediately effective → No manual approval needed
```

#### Configuration Options

**Auto-Generated Leave Settings**:
```ruby
# Auto-generated leaves are always system-approved (no manual approval)
# These settings control other aspects of auto-generated leaves

# Control system behavior
Setting.set('attendance', 'auto_leave_enabled', 'true')  # Enable/disable auto-leave detection
Setting.set('attendance', 'auto_leave_daily_threshold', '5.0')  # Hours threshold for daily leave detection
Setting.set('attendance', 'auto_leave_accumulated_threshold', '9.0')  # Hours threshold for accumulated conversion
```

**Leave Balance Impact**:
```ruby
# Setting to control if auto-generated leaves count against balance
Setting.set('attendance', 'auto_leave_counts_against_balance', 'false')

# Different treatment in leave balance calculation
def calculate_leave_balance_usage(employee, period)
  if period.auto_generated? && !Setting.get('attendance', 'auto_leave_counts_against_balance', 'false').to_bool
    return 0  # Don't deduct from leave balance
  else
    return period.duration_hours  # Standard deduction
  end
end
```

**Notification Settings**:
```ruby
# Notify managers of auto-generated leaves
Setting.set('attendance', 'notify_managers_auto_leave', 'true')

# Notify employees of auto-generated leaves
Setting.set('attendance', 'notify_employees_auto_leave', 'true')

# Notify when auto-generated leaves are created (for awareness, not approval)
Setting.set('attendance', 'notify_auto_leave_created', 'true')
```

#### Reporting and Analytics

**Separate Reporting Streams**:
- **Leave Reports**: Focus on manual leaves (vacation, sick, etc.)
- **Attendance Reports**: Focus on auto-generated leaves (compliance, patterns)

**Key Metrics**:
- Manual leave utilization rates
- Auto-generated leave frequency (attendance issues)
- Patterns of insufficient work hours
- Accumulated hours conversion rates

### 3. Weekend Configuration
- **Flexible Storage**: Two options provided
  - **Option A**: Store in settings table (`company.weekend_days`)
  - **Option B**: Dedicated `weekend_configurations` table (recommended)
- **Default**: Friday-Saturday weekends (5,6)
- **Configurable**: Can be changed per company/organization needs

### 4. Leave Type Distinctions
```ruby
# Manual leave (from approved leave request)
Attendance::Period.create!(
  period_type: 'leave',
  auto_generated: false,  # Manual
  notes: 'Manual leave: Approved vacation'
)

# Auto leave (from attendance logic)
Attendance::Period.create!(
  period_type: 'leave',
  auto_generated: true,   # Auto-generated
  notes: 'Auto-generated leave: Insufficient work hours'
)
```

### 5. Weekend Logic Implementation
```ruby
# Flexible weekend configuration
def get_weekend_days
  # Try dedicated table first
  if WeekendConfiguration.exists?
    config = WeekendConfiguration.active.first
    return JSON.parse(config.weekend_days) if config
  end

  # Fallback to settings - FIXED: use consistent 'attendance' namespace
  Setting.get('attendance', 'weekend_days', '5,6').split(',').map(&:to_i)
end
```

## Codebase Alignment Fixes Applied

### 1. Database Schema Alignment ✅
**Fixed Issues:**
- **Migration Sequence**: Changed from single migration to proper 5-migration sequence
- **Safe Data Deletion**: Use `DELETE` instead of `TRUNCATE` to respect foreign key constraints
- **Column Addition**: Added separate migration to add `auto_generated` column to existing table
- **Sequence Reset**: Added sequence reset for clean auto-increment IDs after deletion

### 2. Period Model Compatibility ✅
**Fixed Issues:**
- **Extended PERIOD_TYPES**: Keep existing types (work, break, late, early_departure, early_arrival) + add 'leave'
- **Existing Scopes**: Maintain all existing scopes (work_periods, break_periods, late_periods, etc.)
- **Table Name**: Explicitly set `self.table_name = 'attendance_periods'` for clarity
- **Existing Methods**: Keep existing helper methods (start_time, end_time, calculate_for_employee_date)
- **New Methods**: Add simplified_type method for mapping complex types to simple types
- **Backward Compatibility**: Preserve existing class methods and API contracts

### 3. Settings Namespace Consistency ✅
**Fixed Issues:**
- **Consistent Namespace**: Use 'attendance' namespace for ALL attendance settings (not mixed with 'company')
- **Weekend Configuration**: Store weekend_days in 'attendance' namespace for consistency
- **Settings Access**: All Setting.get() calls use 'attendance' namespace consistently
- **Seeds Alignment**: Updated seeds to use consistent namespace structure

### 4. Data Transformation Strategy ✅
**Updated Approach:**
```sql
-- Safe approach instead of TRUNCATE (which may fail with foreign keys)
DELETE FROM attendance_periods;
DELETE FROM attendance_events;
DELETE FROM attendance_summaries;

-- Add new column safely
ALTER TABLE attendance_periods ADD COLUMN auto_generated BOOLEAN DEFAULT FALSE NOT NULL;

-- Reset sequences for clean start
ALTER SEQUENCE attendance_periods_id_seq RESTART WITH 1;
```

### 5. Backward Compatibility Maintained ✅
**Preserved Elements:**
- All existing period types remain valid during transition
- All existing scopes and methods preserved
- Existing API endpoints continue to work (with new auto_generated field)
- Existing salary integration can continue using complex period types
- Existing PeriodService.calculate_for_employee_date method signature preserved
- Existing table structure and relationships maintained

### 6. Migration Path Safety ✅
**Safe Transition Strategy:**
1. **Phase 1**: Add new functionality (auto_generated flag, leave type, new models)
2. **Phase 2**: Clean historical data safely with proper deletion order
3. **Phase 3**: Implement new simplified services alongside existing ones
4. **Phase 4**: Gradually migrate to simplified logic with feature flags
5. **Phase 5**: Eventually deprecate complex period types (future phase)

### 7. Settings Structure Aligned ✅
**Consistent Configuration:**
```ruby
# All attendance settings under consistent 'attendance' namespace
Setting.get('attendance', 'monthly_expected_hours', '180.0')
Setting.get('attendance', 'daily_work_hours_threshold', '5.0')
Setting.get('attendance', 'accumulated_hours_threshold', '9.0')
Setting.get('attendance', 'standard_work_hours_per_day', '8.0')
Setting.get('attendance', 'weekend_days', '5,6')  # FIXED: was 'company'
```

### 8. Implementation Safety Ensured ✅
**Risk Mitigation Applied:**
- ✅ No breaking changes to existing functionality
- ✅ Safe data deletion respecting foreign key constraints
- ✅ Proper migration sequence with rollback capabilities
- ✅ Backward compatible model changes with existing method preservation
- ✅ Consistent settings namespace throughout the system
- ✅ Preserved existing API contracts and method signatures
- ✅ Extended rather than replaced existing PERIOD_TYPES
- ✅ Maintained all existing scopes for backward compatibility

### 9. Key Alignment Corrections Made ✅
1. **Period Types**: Extended existing types instead of replacing them
2. **Database Migrations**: Proper sequence with safe deletion approach
3. **Settings Namespace**: Consistent 'attendance' namespace throughout
4. **Model Compatibility**: Preserved all existing methods and scopes
5. **API Compatibility**: Extended existing endpoint functionality (with breaking changes for auto_generated field)
6. **Data Safety**: Safe deletion approach respecting constraints
7. **Migration Strategy**: Additive approach instead of destructive replacement

The plan is now fully aligned with the actual codebase and provides a transformation path with identified breaking changes.

## ⚠️ API Breaking Changes

### Critical API Impact: auto_generated Field

#### 1. New Required Field in All Responses
```json
// BEFORE: Current API response
{
  "data": {
    "id": "123",
    "attributes": {
      "period_type": "work",
      "duration_minutes": 480,
      "notes": "Work period"
    }
  }
}

// AFTER: Updated API response (BREAKING CHANGE)
{
  "data": {
    "id": "123",
    "attributes": {
      "period_type": "work",
      "duration_minutes": 480,
      "auto_generated": false,        // ⚠️ NEW REQUIRED FIELD
      "leave_type": null,             // ⚠️ NEW FIELD
      "notes": "Work period"
    }
  }
}
```

#### 2. New API Parameters
```ruby
# New filtering options (additive - not breaking):
GET /api/attendance/periods?auto_generated=true
GET /api/attendance/periods?leave_type=manual
GET /api/attendance/periods?leave_type=auto_generated
```

#### 3. Leave Period Distinction
```json
// Manual leave (from approved leave request)
{
  "period_type": "leave",
  "auto_generated": false,
  "leave_type": "manual",
  "notes": "Manual leave: Approved vacation"
}

// Auto leave (< 5 hours work detected)
{
  "period_type": "leave",
  "auto_generated": true,
  "leave_type": "auto_generated",
  "notes": "Auto-generated leave: Insufficient work hours"
}
```

#### 4. Impact on API Consumers
- **Frontend Applications**: Must handle new auto_generated field in all responses
- **Mobile Apps**: Need to update response parsing to include new fields
- **Third-party Integrations**: May break if they expect fixed response structure
- **Reporting Systems**: Need to understand manual vs auto-generated leave distinction

#### 5. Migration Requirements for API Consumers
```javascript
// API consumers need to update their code:

// BEFORE: This worked
periods.forEach(period => {
  console.log(period.period_type);
});

// AFTER: This still works, but new fields are available
periods.forEach(period => {
  console.log(period.period_type);        // ✅ Still works
  console.log(period.auto_generated);     // ⚠️ New field (always present)
  console.log(period.leave_type);         // ⚠️ New field (null for non-leave periods)
});
```

### API Compatibility Summary
- ✅ **Endpoints**: All existing endpoints preserved
- ✅ **Parameters**: All existing parameters work the same
- ⚠️ **Response Structure**: New required fields added (BREAKING)
- ⚠️ **Leave Handling**: New distinction between manual/auto leaves (BREAKING)
- ➕ **New Features**: Additional filtering options available
