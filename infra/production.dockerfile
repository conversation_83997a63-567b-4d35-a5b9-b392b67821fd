# syntax=docker/dockerfile:1
# check=error=true

# This Dockerfile is designed for production, not development. Use with <PERSON> or build'n'run by hand:
# docker build -t athar_people .
# docker run -d -p 80:80 -e RAILS_MASTER_KEY=<value from config/master.key> --name athar_people athar_people

# For a containerized dev environment, see Dev Containers: https://guides.rubyonrails.org/getting_started_with_devcontainer.html

# Make sure RUBY_VERSION matches the Ruby version in .ruby-version
ARG RUBY_VERSION=3.4.2

###############
# Build Stage #
###############
FROM docker.io/library/ruby:$RUBY_VERSION-slim AS base

# Define the Bundler version
ARG BUNDLER_VERSION=2.6.5

# Set working directory
WORKDIR /rails

# Install base dependencies including Node.js 22.15.0
RUN apt-get update -qq && \
    apt-get install --no-install-recommends -y \
    curl  \
    libjemalloc2 \
    libvips \
    build-essential \
    libvips \
    ruby-dev \
    libssl-dev \
    libpq-dev \
    libreadline-dev \
    zlib1g-dev \
    libxml2-dev \
    libxslt1-dev \
    libcurl4-openssl-dev \
    libffi-dev \
    ca-certificates \
    gnupg && \
    curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg && \
    echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_22.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list && \
    apt-get update && \
    apt-get install --no-install-recommends -y nodejs=22.15.0-1nodesource1 && \
    rm -rf /var/lib/apt/lists/* /var/cache/apt/archives

# Set production environment
ENV RAILS_ENV="production" \
    BUNDLE_DEPLOYMENT="1" \
    BUNDLE_PATH="/usr/local/bundle" \
    BUNDLE_WITHOUT="development test"

#ENV PATH="/usr/local/bundle/bin:${PATH}"

# Throw-away build stage to reduce size of final image
FROM base AS build

# Install packages needed to build gems
RUN apt-get update -qq && \
    apt-get install --no-install-recommends -y  \
    build-essential  \
    git  \
    pkg-config  \
    libyaml-dev \
    && rm -rf /var/lib/apt/lists /var/cache/apt/archives


# Install Bundler
RUN gem install bundler -v $BUNDLER_VERSION

# Set up fury secret
ARG BUNDLE_GEM__FURY__IO
ENV BUNDLE_GEM__FURY__IO=${BUNDLE_GEM__FURY__IO}

# Install Gems
COPY ../Gemfile Gemfile.lock ./
RUN bundle install --jobs 4 --retry 3 \
    && rm -rf ~/.bundle/ "${BUNDLE_PATH}"/ruby/*/cache "${BUNDLE_PATH}"/ruby/*/bundler/gems/*/.git && \
          bundle exec bootsnap precompile --gemfile

# Copy application code
COPY .. .

# Precompile bootsnap code for faster boot times
RUN bundle exec bootsnap precompile app/ lib/


#################
# Final Stage #
#################
FROM base

# Copy built artifacts: gems, application
COPY --from=build "${BUNDLE_PATH}" "${BUNDLE_PATH}"
COPY --from=build /rails /rails

# Run and own only the runtime files as a non-root user for security
RUN groupadd --system --gid 1000 rails && \
    useradd rails --uid 1000 --gid 1000 --create-home --shell /bin/bash && \
    chown -R rails:rails db log storage tmp
USER 1000:1000

# Entrypoint prepares the database.
ENTRYPOINT ["/rails/bin/docker-entrypoint"]

# Start server via Thruster by default, this can be overwritten at runtime
EXPOSE 80
CMD ["./bin/thrust", "./bin/rails", "server"]
