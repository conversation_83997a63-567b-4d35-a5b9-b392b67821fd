# frozen_string_literal: true

# Grover configuration for HTML to PDF conversion
Grover.configure do |config|
  # PDF options
  config.options = {
    format: 'A4',
    margin: {
      top: '0.5in',
      bottom: '0.5in',
      left: '0.5in',
      right: '0.5in'
    },
    print_background: true,
    prefer_css_page_size: true,
    display_header_footer: false,

    # Performance and reliability options
    timeout: 30_000, # 30 seconds
    wait_until: 'networkidle0',

    # Chrome launch options
    launch_options: {
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--no-first-run',
        '--disable-default-apps',
        '--disable-popup-blocking',
        '--disable-translate',
        '--disable-background-timer-throttling',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows'
      ]
    }
  }
end
