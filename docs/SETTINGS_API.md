# Settings API Documentation

## Overview

The Settings API has been redesigned to use logical key-based identification instead of database IDs. Settings are now accessed using their semantic meaning (namespace + key) rather than arbitrary database IDs.

## API Endpoints

### Base URL
All settings endpoints are under `/api/settings`

### Route Structure
Uses Rails resources with collection routes for logical key access:
- Standard resource routes: `GET /api/settings`, `POST /api/settings`
- Logical key routes: `GET /api/settings/:namespace/:key`

### Authentication
All endpoints require:
- **Authentication**: Session token via `Authorization: Bearer <token>` header
- **Authorization**: Appropriate permissions (`:read`, `:create`, `:update`, `:destroy` on `:setting`)

---

## Endpoints

### 1. List Settings
```http
GET /api/settings
```

**Query Parameters:**
- Standard filtering, sorting, and pagination parameters via Ransack
- Use `filter[namespace_eq]=value` to filter by namespace
- Use `filter[key_cont]=value` to search keys containing text
- Use `sort=namespace` or `sort=key` for sorting

**Examples:**
```bash
# Get all settings
GET /api/settings

# Get only attendance settings
GET /api/settings?filter[namespace_eq]=attendance

# Search settings with keys containing "work"
GET /api/settings?filter[key_cont]=work

# With pagination and sorting
GET /api/settings?page[number]=1&page[size]=10&sort=namespace
```

### 2. Get Specific Setting
```http
GET /api/settings/:namespace/:key
```

**Parameters:**
- `namespace` - Setting namespace (e.g., 'attendance', 'system')
- `key` - Setting key (e.g., 'work_start_time', 'app_name')

**Examples:**
```bash
# Get attendance work start time
GET /api/settings/attendance/work_start_time

# Get system app name
GET /api/settings/system/app_name
```

### 3. Create Setting
```http
POST /api/settings
```

**Request Body:**
```json
{
  "setting": {
    "namespace": "payroll",
    "key": "overtime_rate",
    "value": "1.5",
    "description": "Overtime multiplier rate",
    "is_editable": true
  }
}
```

### 4. Update Setting
```http
PATCH /api/settings/:namespace/:key
```

**Request Body:**
```json
{
  "setting": {
    "value": "08:30",
    "description": "Updated work start time",
    "is_editable": true
  }
}
```

**Examples:**
```bash
# Update attendance work start time
PATCH /api/settings/attendance/work_start_time
Content-Type: application/json

{
  "setting": {
    "value": "08:30",
    "description": "Updated start time"
  }
}
```

### 5. Create or Update Setting
```http
PUT /api/settings/:namespace/:key
```

**Request Body:**
```json
{
  "setting": {
    "value": "1.5",
    "description": "Overtime rate",
    "is_editable": true
  }
}
```

**Behavior:**
- Creates the setting if it doesn't exist (returns 201)
- Updates the setting if it exists (returns 200)
- Respects `is_editable` flag for existing settings

### 6. Delete Setting
```http
DELETE /api/settings/:namespace/:key
```

**Examples:**
```bash
# Delete a test setting
DELETE /api/settings/test/sample_setting
```

---

## Response Format

All responses follow JSON:API standard:

### Success Response
```json
{
  "data": {
    "id": "123",
    "type": "setting",
    "attributes": {
      "namespace": "attendance",
      "key": "work_start_time",
      "value": "09:00",
      "description": "Default work start time",
      "is_editable": true
    }
  }
}
```

### Error Response
```json
{
  "errors": [
    {
      "detail": "Setting not found"
    }
  ]
}
```

---

## Business Logic

### Attendance Settings Auto-Recalculation
When certain attendance settings are updated, the system automatically triggers background recalculation:

**Affected Settings:**
- `attendance/work_start_time`
- `attendance/work_end_time` 
- `attendance/duplicate_threshold_seconds`

**Behavior:**
- Queues `Attendance::BatchPeriodCalculationWorker`
- Recalculates periods for the last 30 days
- Ensures attendance data stays consistent with new settings

### Editable Settings Protection
- Settings with `is_editable: false` cannot be updated or deleted
- Returns 422 Unprocessable Entity with appropriate error message
- Protects system-critical configuration from accidental changes

---

## Current Namespaces

### `attendance`
- `work_start_time` - Default work start time (e.g., "09:00")
- `work_end_time` - Default work end time (e.g., "17:00")
- `duplicate_threshold_seconds` - Threshold for duplicate events (e.g., "60")
- `required_work_minutes` - Required work minutes per day (e.g., "480")
- `break_threshold_minutes` - Maximum break duration (e.g., "120")

### `system` (default namespace)
- General application settings

---

## Migration from ID-based API

### Before (Old API)
```bash
# Had to lookup ID first
GET /api/settings?namespace=attendance
# Find work_start_time has ID 47

# Then update by ID
PATCH /api/settings/47
```

### After (New API)
```bash
# Direct semantic access
PATCH /api/settings/attendance/work_start_time
```

### Benefits
✅ **Intuitive URLs** - Self-documenting endpoints  
✅ **Environment-independent** - Same URLs across all environments  
✅ **No lookup required** - Direct access to settings  
✅ **Better developer experience** - Semantic meaning in URLs  
✅ **RESTful design** - Proper resource identification

---

## Examples

### Complete Workflow
```bash
# 1. List all attendance settings
GET /api/settings?filter[namespace_eq]=attendance

# 2. Search for time-related settings
GET /api/settings?filter[key_cont]=time

# 3. Get specific setting
GET /api/settings/attendance/work_start_time

# 4. Update the setting
PATCH /api/settings/attendance/work_start_time
{
  "setting": {
    "value": "08:30",
    "description": "Earlier start time for winter schedule"
  }
}

# 5. Create new payroll setting
PUT /api/settings/payroll/overtime_rate
{
  "setting": {
    "value": "1.5",
    "description": "Overtime multiplier rate"
  }
}

# 6. List settings with pagination and sorting
GET /api/settings?page[number]=1&page[size]=10&sort=namespace,key
```

### Advanced Filtering Examples
```bash
# Filter by namespace
GET /api/settings?filter[namespace_eq]=attendance

# Search keys containing text
GET /api/settings?filter[key_cont]=work

# Filter editable settings only
GET /api/settings?filter[is_editable_eq]=true

# Multiple filters
GET /api/settings?filter[namespace_eq]=attendance&filter[is_editable_eq]=true

# Sorting
GET /api/settings?sort=namespace          # Sort by namespace ascending
GET /api/settings?sort=-namespace         # Sort by namespace descending
GET /api/settings?sort=namespace,key      # Sort by namespace, then key
```

## Rails Route Helpers

For developers working within the Rails application, the following route helpers are available:

```ruby
# Collection routes
api_settings_path                    # GET /api/settings
api_settings_path                    # POST /api/settings

# Logical key routes
show_by_key_api_settings_path('attendance', 'work_start_time')        # GET /api/settings/attendance/work_start_time
update_by_key_api_settings_path('attendance', 'work_start_time')      # PATCH /api/settings/attendance/work_start_time
create_or_update_by_key_api_settings_path('payroll', 'overtime_rate') # PUT /api/settings/payroll/overtime_rate
destroy_by_key_api_settings_path('test', 'sample_setting')            # DELETE /api/settings/test/sample_setting
```

---

This new API design provides a much more intuitive and maintainable way to manage application settings while preserving all existing functionality and business logic.
