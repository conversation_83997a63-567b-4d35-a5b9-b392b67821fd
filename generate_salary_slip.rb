#!/usr/bin/env ruby
# Temporary script to generate salary slip PDF for a specific salary calculation

puts "🔍 Looking for salary calculation with ID=3..."

# Find the salary calculation
salary_calculation = SalaryCalculation.find_by(id: 3)

if salary_calculation.nil?
  puts "❌ Salary calculation with ID=3 not found!"
  puts "Available salary calculations:"
  SalaryCalculation.limit(10).each do |calc|
    puts "  ID: #{calc.id}, Employee: #{calc.employee.name}, Period: #{calc.period}, Status: #{calc.status}"
  end
  exit 1
end

puts "✅ Found salary calculation:"
puts "  ID: #{salary_calculation.id}"
puts "  Employee: #{salary_calculation.employee.name}"
puts "  Period: #{salary_calculation.period}"
puts "  Status: #{salary_calculation.status}"
puts "  Gross Salary: #{salary_calculation.gross_salary}"
puts "  Net Salary: #{salary_calculation.net_salary}"

# Check if salary calculation is in paid status
if !salary_calculation.paid?
  puts "⚠️  Salary calculation is not in 'paid' status (current: #{salary_calculation.status})"
  puts "🔧 Updating status to 'paid' to allow PDF generation..."
  salary_calculation.update!(status: :paid)
  puts "✅ Status updated to 'paid'"
end

# Check if PDF already exists
if salary_calculation.salary_slip_pdf.attached?
  puts "📄 PDF already exists. Removing old PDF..."
  salary_calculation.salary_slip_pdf.purge
  puts "✅ Old PDF removed"
end

puts "🚀 Generating salary slip PDF..."

begin
  # Create the slip service and generate PDF
  slip_service = Salary::SlipService.new(salary_calculation)
  result = slip_service.generate
  
  if result
    puts "✅ PDF generated successfully!"
    
    if salary_calculation.salary_slip_pdf.attached?
      puts "📄 PDF Details:"
      puts "  Filename: #{salary_calculation.salary_slip_pdf.filename}"
      puts "  Size: #{salary_calculation.salary_slip_pdf.byte_size} bytes"
      puts "  Content Type: #{salary_calculation.salary_slip_pdf.content_type}"
      
      # Try to get the URL if possible
      begin
        url = Rails.application.routes.url_helpers.rails_blob_url(salary_calculation.salary_slip_pdf, host: 'localhost:1235')
        puts "  URL: #{url}"
      rescue => e
        puts "  URL generation failed: #{e.message}"
      end
    else
      puts "⚠️  PDF was generated but not attached properly"
    end
  else
    puts "❌ PDF generation failed!"
  end
  
rescue => e
  puts "❌ Error generating PDF: #{e.message}"
  puts "Stack trace:"
  puts e.backtrace.first(10).join("\n")
end

puts "🏁 Script completed!"
